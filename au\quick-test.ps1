# Quick test runner that avoids unnecessary NuGet restores
# Usage: .\quick-test.ps1 [TestName]

param(
    [string]$TestName = "HotReload_LabelComparison"
)

Write-Host "Quick Test Runner - Avoiding NuGet Restore" -ForegroundColor Green

# Check if we need to restore (only if packages are missing)
$packagesPath = "$env:USERPROFILE\.nuget\packages"
$projectAssetsFile = "LepCore.Test\obj\project.assets.json"

if (-not (Test-Path $projectAssetsFile)) {
    Write-Host "Project assets missing, running restore..." -ForegroundColor Yellow
    dotnet restore LepCore.Test\LepCore.Test.csproj --no-dependencies
}

# Build only if source files are newer than output
$sourceFiles = Get-ChildItem -Path "LepCore.Test\*.cs" -Recurse
$outputFile = "LepCore.Test\bin\Debug\net8.0-windows7.0\LepCore.Test.dll"

$needsBuild = $false
if (-not (Test-Path $outputFile)) {
    $needsBuild = $true
    Write-Host "Output missing, build required" -ForegroundColor Yellow
} else {
    $outputTime = (Get-Item $outputFile).LastWriteTime
    foreach ($file in $sourceFiles) {
        if ($file.LastWriteTime -gt $outputTime) {
            $needsBuild = $true
            Write-Host "Source file $($file.Name) is newer, build required" -ForegroundColor Yellow
            break
        }
    }
}

if ($needsBuild) {
    Write-Host "Building..." -ForegroundColor Yellow
    dotnet build LepCore.Test\LepCore.Test.csproj --no-restore --verbosity minimal
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Build failed!" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "No build needed - all files up to date" -ForegroundColor Green
}

# Run the specific test
Write-Host "Running test: $TestName" -ForegroundColor Cyan
dotnet test LepCore.Test\LepCore.Test.csproj --filter $TestName --no-build --no-restore --verbosity minimal

Write-Host "Test completed" -ForegroundColor Green
