using System;
using System.Drawing;
using System.Drawing.Printing;
using System.Windows.Forms;

namespace LepCore.Test
{
    /// <summary>
    /// Form for side-by-side comparison of two print documents (labels)
    /// </summary>
    public partial class LabelComparisonForm : Form
    {
        private PrintDocument _leftDocument;
        private PrintDocument _rightDocument;
        private Panel _leftPanel;
        private Panel _rightPanel;
        private Label _leftLabel;
        private Label _rightLabel;
        private Bitmap _leftBitmap;
        private Bitmap _rightBitmap;

        public LabelComparisonForm()
        {
            InitializeComponent();
        }

        public LabelComparisonForm(PrintDocument leftDocument, PrintDocument rightDocument, 
            string leftTitle = "Original Label", string rightTitle = "New Label") : this()
        {
            _leftDocument = leftDocument;
            _rightDocument = rightDocument;
            _leftLabel.Text = leftTitle;
            _rightLabel.Text = rightTitle;
            
            RenderDocuments();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = "Label Comparison";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.White;

            // Create main container
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                Padding = new Padding(10)
            };
            
            // Set column and row styles
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));

            // Create labels
            _leftLabel = new Label
            {
                Text = "Original Label",
                Font = new Font("Arial", 12, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill,
                BackColor = Color.LightGray
            };

            _rightLabel = new Label
            {
                Text = "New Label",
                Font = new Font("Arial", 12, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill,
                BackColor = Color.LightBlue
            };

            // Create panels for rendering
            _leftPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White,
                AutoScroll = true
            };

            _rightPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White,
                AutoScroll = true
            };

            // Add paint events
            _leftPanel.Paint += LeftPanel_Paint;
            _rightPanel.Paint += RightPanel_Paint;

            // Add controls to table layout
            mainPanel.Controls.Add(_leftLabel, 0, 0);
            mainPanel.Controls.Add(_rightLabel, 1, 0);
            mainPanel.Controls.Add(_leftPanel, 0, 1);
            mainPanel.Controls.Add(_rightPanel, 1, 1);

            this.Controls.Add(mainPanel);
            this.ResumeLayout(false);
        }

        private void RenderDocuments()
        {
            try
            {
                if (_leftDocument != null)
                {
                    _leftBitmap = RenderDocumentToBitmap(_leftDocument);
                }

                if (_rightDocument != null)
                {
                    _rightBitmap = RenderDocumentToBitmap(_rightDocument);
                }

                // Refresh panels to trigger paint events
                _leftPanel.Invalidate();
                _rightPanel.Invalidate();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error rendering documents: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private Bitmap RenderDocumentToBitmap(PrintDocument document)
        {
            // Create a bitmap to render the document
            var bitmap = new Bitmap(800, 1200); // A4-ish proportions

            using (var graphics = Graphics.FromImage(bitmap))
            {
                graphics.Clear(Color.White);

                // Create print page event args
                var pageSettings = document.DefaultPageSettings;
                var printableArea = new Rectangle(
                    pageSettings.Margins.Left,
                    pageSettings.Margins.Top,
                    pageSettings.PaperSize.Width - pageSettings.Margins.Left - pageSettings.Margins.Right,
                    pageSettings.PaperSize.Height - pageSettings.Margins.Top - pageSettings.Margins.Bottom
                );

                var args = new PrintPageEventArgs(
                    graphics,
                    printableArea,
                    new Rectangle(0, 0, pageSettings.PaperSize.Width, pageSettings.PaperSize.Height),
                    pageSettings
                );

                try
                {
                    // Try to trigger the print page event to render the document
                    var printPageMethod = document.GetType().GetMethod("OnPrintPage",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                    if (printPageMethod != null)
                    {
                        printPageMethod.Invoke(document, new object[] { args });
                    }
                    else
                    {
                        // Fallback: try to use the PrintPage event directly
                        document.PrintPage += (sender, e) => { };
                        var printPageEvent = document.GetType().GetField("PrintPage",
                            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                        if (printPageEvent?.GetValue(document) is PrintPageEventHandler handler)
                        {
                            handler.Invoke(document, args);
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Draw error message if rendering fails
                    graphics.DrawString($"Error rendering: {ex.Message}",
                        new Font("Arial", 12), Brushes.Red, new PointF(10, 10));
                }
            }

            return bitmap;
        }

        private void LeftPanel_Paint(object sender, PaintEventArgs e)
        {
            if (_leftBitmap != null)
            {
                // Scale the bitmap to fit the panel while maintaining aspect ratio
                var panel = sender as Panel;
                var scaleX = (float)panel.Width / _leftBitmap.Width;
                var scaleY = (float)panel.Height / _leftBitmap.Height;
                var scale = Math.Min(scaleX, scaleY);

                var scaledWidth = (int)(_leftBitmap.Width * scale);
                var scaledHeight = (int)(_leftBitmap.Height * scale);

                var x = (panel.Width - scaledWidth) / 2;
                var y = (panel.Height - scaledHeight) / 2;

                e.Graphics.DrawImage(_leftBitmap, x, y, scaledWidth, scaledHeight);
            }
        }

        private void RightPanel_Paint(object sender, PaintEventArgs e)
        {
            if (_rightBitmap != null)
            {
                // Scale the bitmap to fit the panel while maintaining aspect ratio
                var panel = sender as Panel;
                var scaleX = (float)panel.Width / _rightBitmap.Width;
                var scaleY = (float)panel.Height / _rightBitmap.Height;
                var scale = Math.Min(scaleX, scaleY);

                var scaledWidth = (int)(_rightBitmap.Width * scale);
                var scaledHeight = (int)(_rightBitmap.Height * scale);

                var x = (panel.Width - scaledWidth) / 2;
                var y = (panel.Height - scaledHeight) / 2;

                e.Graphics.DrawImage(_rightBitmap, x, y, scaledWidth, scaledHeight);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _leftBitmap?.Dispose();
                _rightBitmap?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
