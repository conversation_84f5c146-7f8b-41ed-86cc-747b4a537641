﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "LepCore", "LepCore\LepCore.csproj", "{CF4055C3-8EA1-45DA-AB5E-F9E6F1745D57}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "code", "code", "{60139096-31FC-4FF3-9C29-603C415DF58D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "main", "code\main\main.csproj", "{8ED87181-2165-4A63-B1B3-88FBA8555B64}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "lumen", "lumen\lumen.csproj", "{6580CABC-A2A2-4C71-BCE6-315FCCDBE7AB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "LepCore.DTOs", "LepCore.DTOs\LepCore.DTOs.csproj", "{3DFC8FAD-1F02-49DC-BE81-12CC5C70845B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "LepCore.Test", "LepCore.Test\LepCore.Test.csproj", "{BC8D682B-DC1B-4F7F-BDD5-AF28FBD09E29}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "code (2)", "code (2)", "{1A4365D8-9816-407F-AA59-9A73CFF1F22F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "WestpacCSVtoMyobCloud", "WestpacCSVtoMyobCloud\WestpacCSVtoMyobCloud.csproj", "{D3F5D219-D5AB-49E0-B221-B685C36A3B63}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "scanner-service", "code\scanner-service\scanner-service.csproj", "{4339D3F6-DF0F-417B-B68E-27A102F09A8B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "LepInvoicer", "LepInvoicer\LepInvoicer.csproj", "{1529C042-7722-45EA-B502-EDB8715C708F}"
EndProject
Project("{6EC3EE1D-3C4E-46DD-8F32-0CC8E7565705}") = "LepInvoicerFSharp", "LepInvoicerFSharp\LepInvoicerFSharp.fsproj", "{51EE1223-0509-4EBB-8B21-B12C88A863EB}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{CF4055C3-8EA1-45DA-AB5E-F9E6F1745D57}.Debug|Any CPU.ActiveCfg = Debug|x64
		{CF4055C3-8EA1-45DA-AB5E-F9E6F1745D57}.Debug|Any CPU.Build.0 = Debug|x64
		{CF4055C3-8EA1-45DA-AB5E-F9E6F1745D57}.Debug|x64.ActiveCfg = Debug|x64
		{CF4055C3-8EA1-45DA-AB5E-F9E6F1745D57}.Debug|x64.Build.0 = Debug|x64
		{CF4055C3-8EA1-45DA-AB5E-F9E6F1745D57}.Release|Any CPU.ActiveCfg = Release|x64
		{CF4055C3-8EA1-45DA-AB5E-F9E6F1745D57}.Release|Any CPU.Build.0 = Release|x64
		{CF4055C3-8EA1-45DA-AB5E-F9E6F1745D57}.Release|x64.ActiveCfg = Release|x64
		{CF4055C3-8EA1-45DA-AB5E-F9E6F1745D57}.Release|x64.Build.0 = Release|x64
		{8ED87181-2165-4A63-B1B3-88FBA8555B64}.Debug|Any CPU.ActiveCfg = Debug|x64
		{8ED87181-2165-4A63-B1B3-88FBA8555B64}.Debug|Any CPU.Build.0 = Debug|x64
		{8ED87181-2165-4A63-B1B3-88FBA8555B64}.Debug|x64.ActiveCfg = Debug|x64
		{8ED87181-2165-4A63-B1B3-88FBA8555B64}.Debug|x64.Build.0 = Debug|x64
		{8ED87181-2165-4A63-B1B3-88FBA8555B64}.Release|Any CPU.ActiveCfg = Release|x64
		{8ED87181-2165-4A63-B1B3-88FBA8555B64}.Release|Any CPU.Build.0 = Release|x64
		{8ED87181-2165-4A63-B1B3-88FBA8555B64}.Release|x64.ActiveCfg = Release|x64
		{8ED87181-2165-4A63-B1B3-88FBA8555B64}.Release|x64.Build.0 = Release|x64
		{6580CABC-A2A2-4C71-BCE6-315FCCDBE7AB}.Debug|Any CPU.ActiveCfg = Debug|x64
		{6580CABC-A2A2-4C71-BCE6-315FCCDBE7AB}.Debug|Any CPU.Build.0 = Debug|x64
		{6580CABC-A2A2-4C71-BCE6-315FCCDBE7AB}.Debug|x64.ActiveCfg = Debug|x64
		{6580CABC-A2A2-4C71-BCE6-315FCCDBE7AB}.Debug|x64.Build.0 = Debug|x64
		{6580CABC-A2A2-4C71-BCE6-315FCCDBE7AB}.Release|Any CPU.ActiveCfg = Release|x64
		{6580CABC-A2A2-4C71-BCE6-315FCCDBE7AB}.Release|Any CPU.Build.0 = Release|x64
		{6580CABC-A2A2-4C71-BCE6-315FCCDBE7AB}.Release|x64.ActiveCfg = Release|x64
		{6580CABC-A2A2-4C71-BCE6-315FCCDBE7AB}.Release|x64.Build.0 = Release|x64
		{3DFC8FAD-1F02-49DC-BE81-12CC5C70845B}.Debug|Any CPU.ActiveCfg = Debug|x64
		{3DFC8FAD-1F02-49DC-BE81-12CC5C70845B}.Debug|Any CPU.Build.0 = Debug|x64
		{3DFC8FAD-1F02-49DC-BE81-12CC5C70845B}.Debug|x64.ActiveCfg = Debug|x64
		{3DFC8FAD-1F02-49DC-BE81-12CC5C70845B}.Debug|x64.Build.0 = Debug|x64
		{3DFC8FAD-1F02-49DC-BE81-12CC5C70845B}.Release|Any CPU.ActiveCfg = Release|x64
		{3DFC8FAD-1F02-49DC-BE81-12CC5C70845B}.Release|Any CPU.Build.0 = Release|x64
		{3DFC8FAD-1F02-49DC-BE81-12CC5C70845B}.Release|x64.ActiveCfg = Release|x64
		{3DFC8FAD-1F02-49DC-BE81-12CC5C70845B}.Release|x64.Build.0 = Release|x64
		{BC8D682B-DC1B-4F7F-BDD5-AF28FBD09E29}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BC8D682B-DC1B-4F7F-BDD5-AF28FBD09E29}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BC8D682B-DC1B-4F7F-BDD5-AF28FBD09E29}.Debug|x64.ActiveCfg = Debug|x64
		{BC8D682B-DC1B-4F7F-BDD5-AF28FBD09E29}.Debug|x64.Build.0 = Debug|x64
		{BC8D682B-DC1B-4F7F-BDD5-AF28FBD09E29}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BC8D682B-DC1B-4F7F-BDD5-AF28FBD09E29}.Release|Any CPU.Build.0 = Release|Any CPU
		{BC8D682B-DC1B-4F7F-BDD5-AF28FBD09E29}.Release|x64.ActiveCfg = Release|x64
		{BC8D682B-DC1B-4F7F-BDD5-AF28FBD09E29}.Release|x64.Build.0 = Release|x64
		{D3F5D219-D5AB-49E0-B221-B685C36A3B63}.Debug|Any CPU.ActiveCfg = Debug|x64
		{D3F5D219-D5AB-49E0-B221-B685C36A3B63}.Debug|Any CPU.Build.0 = Debug|x64
		{D3F5D219-D5AB-49E0-B221-B685C36A3B63}.Debug|x64.ActiveCfg = Debug|x64
		{D3F5D219-D5AB-49E0-B221-B685C36A3B63}.Debug|x64.Build.0 = Debug|x64
		{D3F5D219-D5AB-49E0-B221-B685C36A3B63}.Release|Any CPU.ActiveCfg = Release|x64
		{D3F5D219-D5AB-49E0-B221-B685C36A3B63}.Release|Any CPU.Build.0 = Release|x64
		{D3F5D219-D5AB-49E0-B221-B685C36A3B63}.Release|x64.ActiveCfg = Release|x64
		{D3F5D219-D5AB-49E0-B221-B685C36A3B63}.Release|x64.Build.0 = Release|x64
		{4339D3F6-DF0F-417B-B68E-27A102F09A8B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4339D3F6-DF0F-417B-B68E-27A102F09A8B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4339D3F6-DF0F-417B-B68E-27A102F09A8B}.Debug|x64.ActiveCfg = Debug|Any CPU
		{4339D3F6-DF0F-417B-B68E-27A102F09A8B}.Debug|x64.Build.0 = Debug|Any CPU
		{4339D3F6-DF0F-417B-B68E-27A102F09A8B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4339D3F6-DF0F-417B-B68E-27A102F09A8B}.Release|Any CPU.Build.0 = Release|Any CPU
		{4339D3F6-DF0F-417B-B68E-27A102F09A8B}.Release|x64.ActiveCfg = Release|Any CPU
		{4339D3F6-DF0F-417B-B68E-27A102F09A8B}.Release|x64.Build.0 = Release|Any CPU
		{1529C042-7722-45EA-B502-EDB8715C708F}.Debug|Any CPU.ActiveCfg = Debug|x64
		{1529C042-7722-45EA-B502-EDB8715C708F}.Debug|Any CPU.Build.0 = Debug|x64
		{1529C042-7722-45EA-B502-EDB8715C708F}.Debug|x64.ActiveCfg = Debug|x64
		{1529C042-7722-45EA-B502-EDB8715C708F}.Debug|x64.Build.0 = Debug|x64
		{1529C042-7722-45EA-B502-EDB8715C708F}.Release|Any CPU.ActiveCfg = Release|x64
		{1529C042-7722-45EA-B502-EDB8715C708F}.Release|Any CPU.Build.0 = Release|x64
		{1529C042-7722-45EA-B502-EDB8715C708F}.Release|x64.ActiveCfg = Release|x64
		{1529C042-7722-45EA-B502-EDB8715C708F}.Release|x64.Build.0 = Release|x64
		{51EE1223-0509-4EBB-8B21-B12C88A863EB}.Debug|Any CPU.ActiveCfg = Debug|x64
		{51EE1223-0509-4EBB-8B21-B12C88A863EB}.Debug|Any CPU.Build.0 = Debug|x64
		{51EE1223-0509-4EBB-8B21-B12C88A863EB}.Debug|x64.ActiveCfg = Debug|x64
		{51EE1223-0509-4EBB-8B21-B12C88A863EB}.Debug|x64.Build.0 = Debug|x64
		{51EE1223-0509-4EBB-8B21-B12C88A863EB}.Release|Any CPU.ActiveCfg = Release|x64
		{51EE1223-0509-4EBB-8B21-B12C88A863EB}.Release|Any CPU.Build.0 = Release|x64
		{51EE1223-0509-4EBB-8B21-B12C88A863EB}.Release|x64.ActiveCfg = Release|x64
		{51EE1223-0509-4EBB-8B21-B12C88A863EB}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{8ED87181-2165-4A63-B1B3-88FBA8555B64} = {60139096-31FC-4FF3-9C29-603C415DF58D}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {4E0F2A3B-A30A-4197-B589-2E0A304F1CAB}
	EndGlobalSection
EndGlobal
