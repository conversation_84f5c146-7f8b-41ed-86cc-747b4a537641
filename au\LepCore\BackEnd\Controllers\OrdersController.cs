using AutoMapper;
using FastReport;
using FastReport.Export.PdfSimple;
using FastReport.Export.PdfSimple.PdfCore;
using lep;
using lep.address.impl;
using lep.configuration;
using lep.contact.impl;
using lep.content;
using lep.courier;
using lep.despatch;
using lep.despatch.impl;
using lep.email;
using lep.extensionmethods;
using lep.freight;
using lep.job;
using lep.job.impl;
using lep.jobmonitor;
using lep.jobmonitor.impl;
using lep.order;
using lep.pricing;
using lep.promotion;
using lep.run;
using lep.src.onlineTxn.impl.Westpac;
using lep.user;
using lep.user.impl;
using LepCore.Dto;

using Serilog;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using NHibernate.Criterion;

using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Claims;
using System.Text;
using System.Text.Encodings.Web;
using System.Web;
using static lep.job.JobTypeOptions;
using static lep.PaymentTermsOptions;
using static System.Net.HttpStatusCode;
using Restrictions = NHibernate.Criterion.Restrictions;
using System.Drawing.Printing;
using lep.barcode;
using FastReport.Export.Html;
using System.Threading.Tasks;
using NHibernate.Impl;
using System.Diagnostics;
using System.Net.Http;
using Stripe;
using Order = NHibernate.Criterion.Order;
using System.Xml.Linq;
using lep.order.impl;

namespace LepCore.Controllers
{


	[ApiExplorerSettings(IgnoreApi = true)]
	[Produces("application/json")]
	[Route("api/[controller]")]
	[Authorize]
	[ResponseCache(Duration = 0, VaryByHeader = "*", Location = ResponseCacheLocation.None, NoStore = true)]

	public partial class OrdersController : Controller
	{


		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
		private readonly IConfigurationApplication _configApp;

		private readonly ICourierApplication _courierApplication;
		private readonly IEmailApplication _emailApp;
		private readonly IFreightApplication _freightApplication;
		private readonly IJobApplication _jobApp;
		private readonly JobBoardDTOHelper _jobBoardDTOHelper;
		private readonly IOrderApplication _orderApp;
		private readonly IPricingEngine _pricingEngine;
		private readonly IPromotionApplication _promotionApplication;
		private readonly IUserApplication _userApplication;
		private readonly LabelPrinterApplication _labelPrintApp;
		private readonly IRunApplication _runApplication;

		private readonly IPackageApplication _packageApplication;
		private bool _currentUserIsCust;
		private bool _currentUserIsStaff;
		private bool _currentUserIsAnonymousWLCustomer;
		private bool _currentUserIsLoggedInWLCustomer;

		private IUser _currentUser;

		private IMapper _mapper;
		private IMemoryCache _cache;
		private IConfigurationRoot _config;
		private CachedAccessTo _cachedAccessTo;

		private IHttpContextAccessor _httpContextAccessor;

		public OrdersController(
			IUserApplication ua,
			IOrderApplication oa,
			IJobApplication ja,
			IEmailApplication ea,
			IPricingEngine pe,
			IFreightApplication fa,
			ICourierApplication ca,
			IPromotionApplication pa,
			IRunApplication ra,
			IConfigurationApplication cona,
			JobBoardDTOHelper jbh,
			LabelPrinterApplication labelPrintApp,
			IMapper mapper,
			IConfigurationRoot config,
			IHttpContextAccessor httpContextAccessor,
			IMemoryCache cache,
			IPackageApplication packApp,
			CachedAccessTo cachedAccessTo
		)
		{
			_orderApp = oa;
			_jobApp = ja;
			_emailApp = ea;
			_userApplication = ua;
			_pricingEngine = pe;
			_freightApplication = fa;
			_courierApplication = ca;
			_promotionApplication = pa;
			_configApp = cona;
			_jobBoardDTOHelper = jbh;
			_runApplication = ra;
			_labelPrintApp = labelPrintApp;
			_packageApplication = packApp;
			_mapper = mapper;
			_config = config;
			_httpContextAccessor = httpContextAccessor;
			_cache = cache;
			_cachedAccessTo = cachedAccessTo;
		}


		public override void OnActionExecuting(ActionExecutingContext context)
		{
			if (context.HttpContext.Request.Path.ToString().Contains("WestPackPayment"))
				return;

			var userId = User.Claims.Where(c => c.Type == "UserId").Select(c => Convert.ToInt32(c.Value)).FirstOrDefault();
			_currentUser = _userApplication.GetUser(userId);
			//_currentUserIsCust = !_currentUser?.IsStaff ?? false;
			//	_currentUserIsStaff = _currentUser?.IsStaff ?? false;
			var rl = User.Claims.ToList();

			var roles = User.Claims.Where(c => c.Type == ClaimTypes.Role).Select(c => c.Value).ToList();

			_currentUserIsStaff = roles.Contains(LepRoles.SuperAdministrator) || roles.Contains(LepRoles.Administrator) || roles.Contains(LepRoles.Staff); // TODO

			_currentUserIsCust = roles.Contains(LepRoles.Customer);

			_currentUserIsAnonymousWLCustomer = roles.Contains(LepRoles.AnonymousWLCustomer);
			_currentUserIsLoggedInWLCustomer = roles.Contains(LepRoles.LoggedInWLCustomer);
		}




		[ApiExplorerSettings(IgnoreApi = false)]
		[HttpPost("MyOrders2")]
		[HttpGet("MyOrders2")]
		[Authorize(Roles = "Customer,LoggedInWLCustomer")]
		[ResponseCache(Duration = 30, VaryByQueryKeys = new[] { "*" }, Location = ResponseCacheLocation.Client)]
		public IActionResult MyOrdersView2(OrderSearchCriteriaDto sp)
		{
			if (sp == null) sp = new OrderSearchCriteriaDto();
			bool? cancel = true;

			var bOrderStatusUsed = sp.OrderStatuses.Any() && string.IsNullOrEmpty(sp.OrderNr);

			var types = new List<IJobTemplate>();
			if (sp.JobTypes != null) types = sp.JobTypes.Select(x => _jobApp.GetJobTemplate((int)x)).ToList();

			NHibernate.ICriteria ordersC = null;
			if (_currentUserIsCust)
			{
				string userId = User.Claims.Where(c => c.Type == "UserId").Select(c => c.Value).FirstOrDefault();
				ICustomerUser user = (ICustomerUser)_userApplication.GetUser(Convert.ToInt32(userId));

				ordersC = _orderApp.OrderCriteriaCust(user, sp.OrderNr, sp.OrderStatuses, types, cancel, null, null, null,
						(bool?)sp.IsRejected, (bool?)sp.IsWaitingApproval, (bool?)sp.IsQuoteRequired, sp.IsWhiteLabel, sp.WLCustomerId, sp.IsWLOrderPaidFor);

				if (!string.IsNullOrEmpty(sp.Customer))
				{
					var x = sp.Customer.Trim();
					var d = new Disjunction();
					d.Add(Expression.Sql($@"((this_.WLContact like '%{x}%' ) )"));//or (1=1)
					d.Add(Restrictions.Like("WLCustomerName", x, MatchMode.Anywhere));
					ordersC.Add(d);
				}
			}

			if (_currentUserIsLoggedInWLCustomer)
			{
				string userId = User.Claims.Where(c => c.Type == "SubCustId").Select(c => c.Value).FirstOrDefault();
				ordersC = _orderApp.OrderCriteriaCust(null, sp.OrderNr, sp.OrderStatuses, types, cancel, null, null, null,
						(bool?)sp.IsRejected, (bool?)sp.IsWaitingApproval, (bool?)sp.IsQuoteRequired, sp.IsWhiteLabel, Convert.ToInt32(userId), sp.IsWLOrderPaidFor);
			}

			//Log.Information( Utils.GetGeneratedSql(ordersC) );
			var sortOrderA = new[] { NHibernate.Criterion.Order.Desc("Id") };
			var list = Utils.GetPagedResult2<CustomerOrderSummaryDtoWithJobs>(_orderApp.BaseSession, ordersC, sp.Page, 10, o => _mapper.Map<CustomerOrderSummaryDtoWithJobs>(o), sortOrderA);
			//var list = Utils.GetPagedResult<CustomerOrderSummaryDtoWithJobs>(ordersC, sp.Page, 10, o => _mapper.Map<CustomerOrderSummaryDtoWithJobs>(o), sortOrderA);

			foreach (var o in list.List)
			{
				var oDto = _mapper.Map<CustomerOrderSummaryDtoWithJobs>(o);
				foreach (var job in o.Jobs)
				{
					var j = _jobApp.GetJob(job.Id);
					var p = _cachedAccessTo.JobProgress(j);
					var jDto = oDto.Jobs.First(jx => jx.Id == job.Id);
					jDto.P = p.progress == 100 ? 99 : p.progress;
					jDto.Thumbs = _cachedAccessTo.Thumbnails(j).Take(2).Select(f => f.Name).ToList();
				}
			}

			var result = new
			{
				list = list,
				bOrderStatusUsed = bOrderStatusUsed
			};

			Log.Information($"{_currentUser.Username}  opening Cusomer Orders list page " + toJson(sp));

			return new OkObjectResult(result);
		}

		// called from staff pages to get a list of orders
		[Authorize(Roles = LepRoles.Staff)]
		[HttpGet("CustOrders")]
		[ReturnBadRequestOnModelError]
		[Produces(typeof(IList<OrderSummaryDto>))]
		public IActionResult CustOrders([FromQuery] OrderSearchCriteriaDto dto)
		{
			Log.Information("{User} opening Orders list page. {@OrderSearchCriteriaDto}", _currentUser.Username);

			var sp = _mapper.Map<OrderSearchCriteria>(dto);
			var jobTypeOptions = sp.JobType;

			var types = dto.JobTypes.Select(x => _jobApp.GetJobTemplate(x)).ToList();

			if (dto.JobType != null)
			{
				types.Add(_jobApp.GetJobTemplate(dto.JobType.Value));
			}

			var nontypes = new List<IJobTemplate>();
			if (sp.IsNonBusinessCard)
			{
				nontypes.Add(_jobApp.GetJobTemplate(BusinessCard));
				nontypes.Add(_jobApp.GetJobTemplate(BusinessCardNdd));
				nontypes.Add(_jobApp.GetJobTemplate(BusinessCardSdd));
				nontypes.Add(_jobApp.GetJobTemplate(Postcard));
			}

			RunCelloglazeOptions? cello = null;
			if (sp.Celloglaze != null) cello = (RunCelloglazeOptions)sp.Celloglaze;

			var criteria = _orderApp.OrderCriteria(sp.Customer,
										  sp.OrderNr,
										  sp.JobNr,
										  sp.OrderStatus.ToString(),
										  sp.IsNewOrder,
										  sp.IsOnhold,
										  sp.IsOnlyUrgentOrder,
										  sp.IsCorrectedOrder,
										  sp.IsOnPrepay,
										  sp.IsOpenOrder,
										  sp.IsAwaitingPayment,
										  sp.IsWithdraw,
										  sp.IsWaitingApproval,
										  sp.IsQuoteRequired,
										  sp.IsRejected,
										  types,
										  nontypes,
										  cello,
										  sp.Size,
										  sp.Stock,
										  sp.IsOrderWithDigitalJob,
										  sp.IsOrderWithOutworkJob,
										  dto.Facility,
										  dto.IsWhiteLabel,
										  dto.IsPaidFor,
										  (dto.IsUnableToMeetPrice ?? false),
										  sp.HideOnHoldOrders,
										  sp.ShowOnlyOnHoldOrders);

			Order sortOrder = null;
			if (!string.IsNullOrEmpty(sp.SortField))
				sortOrder = new Order(sp.SortField, sp.SortDir == "true");
			else
				sortOrder = new Order("SubmissionDate", false);
			//sortOrder = new Order("j2.RequiredByDate", false);

			var sortOrderA = new[] { sortOrder };

			var list = Utils.GetPagedResult2<OrderSummaryDto>(_orderApp.BaseSession, criteria, sp.Page, 20, o => _mapper.Map<OrderSummaryDto>(o), sortOrderA);

			list.List.ForEach(o => o.InUseBy = _cachedAccessTo.GetOrderInUseBy(o.Id)); // fill in order in use by from cache
			return new OkObjectResult(list);
		}

		private static string toJson(OrderSearchCriteriaDto dto)
		{
			return JsonConvert.SerializeObject(dto,
									 new JsonSerializerSettings
									 {
										 NullValueHandling = NullValueHandling.Ignore,
										 DefaultValueHandling = DefaultValueHandling.Ignore
									 });
		}

		[HttpPost("Order/{orderId:int}/small")]
		[Produces(typeof(OrderSummaryDtoWithJobs))]
		public IActionResult GetOrder(int orderId)
		{
			try
			{
				var order = _orderApp.GetOrder(orderId);

				if (_currentUserIsCust && order.Customer.Id != _currentUser.Id)
					return new StatusCodeResult(403);

				if (_currentUserIsCust && order.IsDeleted)
					return new StatusCodeResult((int)HttpStatusCode.NoContent);

				if (_currentUserIsAnonymousWLCustomer && order.Customer.Id != (_currentUser as ICustomerUser).ParentCustomer.Id)
					return new StatusCodeResult((int)Forbidden);

				var promotionApplyLog = "";

				if (!_currentUserIsAnonymousWLCustomer)
				{
					promotionApplyLog = _orderApp.ScatteredLogic_AutoPopulatePromotion(order, _currentUser);
				}

				Log.Information("{User} opening Order {OrderId}", _currentUser.Username, orderId);

				var orderDto = _mapper.Map<OrderSummaryDtoWithJobs>(order);
				orderDto.Visibility = GetFlagsRelatedToOrder(order);
				orderDto.Visibility.promotionApplyLog = promotionApplyLog;

				return new OkObjectResult(orderDto);
			}
			catch (Exception ex)
			{
				return new BadRequestObjectResult(new { error = ex });
			}
		}

		[Authorize(Roles = LepRoles.Staff)]
		[HttpGet("GetDispatchDate/{orderId:int}/{ifSubmittedAt:DateTime?}")]
		public IActionResult GetDispatchDate([FromRoute] int orderId, [FromRoute] DateTime? ifSubmittedAt)
		{
			try
			{
				var order = _orderApp.GetOrder(orderId);
				bool estimated = false;
				if (ifSubmittedAt == null)
				{
					ifSubmittedAt = DateTime.Now;
				}
				var date = _orderApp.GetDispatchDate(order, ifSubmittedAt.Value, out estimated);
				return new OkObjectResult(date);
			}
			catch (Exception ex)
			{
				return new BadRequestObjectResult(new { error = ex });
			}
		}

		[HttpGet("SetDispatchDate/{orderId:int}")]
		public IActionResult SetDispatchDate(int orderId)
		{
			try
			{
				var order = _orderApp.GetOrder(orderId);
				bool estimated = false;
				var date = _orderApp.GetDispatchDate(order, DateTime.Now, out estimated);
				order.DispatchEst = date;
				_orderApp.BaseSave(order);
				return new OkObjectResult(date);
			}
			catch (Exception ex)
			{
				return new BadRequestObjectResult(new { error = ex });
			}
		}

		[HttpGet("Order/{orderId:int}/sendConnote")]
		public IActionResult SendConnote(int orderId)
		{
			try
			{
				var order = _orderApp.GetOrder(orderId);
				if (order == null) return new BadRequestObjectResult(new { error = "cant find order" });
				//_emailApp.SendCourierMail(order);
				_emailApp.SendNotification(order, ContentType.CourierDispatch);
				return Ok();
			}
			catch (Exception ex)
			{
				return new BadRequestObjectResult(new { error = ex });
			}
		}




		[HttpGet("Dispatch/{jobId:int}")]
		[HttpGet("DispatchJob/{jobId:int}")]
		[Produces(typeof(IOrder))]
		public IActionResult DispatchJob(int jobId)
		{
			var job = _jobApp.GetJob(jobId);
			if (job == null) return NotFound();
			var order = job.Order;
			if (order == null) return NotFound();

			if (!_currentUser.IsStaff && _currentUser.Id != order.Customer.Id) return new StatusCodeResult(403);
			job.Status = JobStatusOptions.Packed;

			_orderApp.BaseSave(order);
			Log.Information($"{_currentUser.Username}  old dispather opening  {order.Id} and packing job {job.Id}");
			var orderDto = _mapper.Map<OrderDtoLarge>(order);
			//orderDto.Visibility = GetFlagsRelatedToOrder(order);

			//foreach (var j in order.Jobs)
			return new OkObjectResult(orderDto);
		}

		public class DispatchJobRequest
		{
			public int JobId { get; set; }
			public Dictionary<string, string> Printers { get; set; }

			public Facility Facility { get; set; }
		}

		[HttpPost("DispatchRefresh")]
		[Produces(typeof(IOrder))]
		public IActionResult DispatchRefresh([FromBody] DispatchJobRequest dr, [FromServices] IBarcodeService barcodeService)
		{
			try
			{
				var job = _jobApp.GetJob(dr.JobId);
				if (job == null) return NotFound("Job not found");
				var order = job.Order;
				if (order == null) return NotFound("Order not found");

				if (!_currentUser.IsStaff && _currentUser.Id != order.Customer.Id) return new StatusCodeResult(403);

				Log.Information($"{_currentUser.Username}  dispather opening  {order.Id}");
				var orderDto = _mapper.Map<OrderDtoLarge>(order);
				orderDto.Visibility = _labelPrintApp.GetDispatcherFlags(job);
				return new OkObjectResult(orderDto);
			}
			catch (Exception ex)
			{
				Log.Error(ex, ex.Message);
				return null;
			}
		}


		[HttpPost("DispatchJob2")]
		[Produces(typeof(IOrder))]
		public IActionResult DispatchJob2([FromBody] DispatchJobRequest dr, [FromServices] IBarcodeService barcodeService)
		{
			try
			{
				var job = _jobApp.GetJob(dr.JobId);
				if (job == null) return NotFound("Job not found");
				var order = job.Order;
				if (order == null) return NotFound("Order not found");

				if (!_currentUser.IsStaff && _currentUser.Id != order.Customer.Id) return new StatusCodeResult(403);

				if (job.Status == JobStatusOptions.Packed)
				{
					return DispatchRefresh(dr, barcodeService);
				}

				var dispathProcessOutcome = _labelPrintApp.DoDispatchProcessUnifiedNonBC(job, _currentUser, dr.Printers, dr.Facility);
				barcodeService.MailNotification(job);
				_orderApp.BaseSave(order);

				Log.Information($"{_currentUser.Username}  dispather opening  {order.Id}");
				var orderDto = _mapper.Map<OrderDtoLarge>(order);
				orderDto.Visibility = dispathProcessOutcome;
				//foreach (var j in order.Jobs)
				return new OkObjectResult(orderDto);
			}
			catch (Exception ex)
			{
				Log.Error(ex, ex.Message);
				return null;
			}
		}

		[HttpPost("Order/{orderId:int}/SetJobsInOrderToDispatched/{facility}")]
		[Produces(typeof(IOrder))]
		public IActionResult SetJobsInOrderToDispatched(int orderId, Facility facility)
		{
			var order = _orderApp.GetOrder(orderId);

			if (order == null) return NotFound();



			order.Jobs.Where(_ => _.Facility == facility).ForEach(_ => _.SetStatus(JobStatusOptions.Dispatched, _currentUser));
			_orderApp.BaseSave(order);
			Log.Information("{User} Dispatching order in {facility}", _currentUser.Username, orderId);

			var orderDto = _mapper.Map<OrderDtoLarge>(order);
			orderDto.Visibility = GetFlagsRelatedToOrder(order);

			foreach (var j in order.Jobs)
				orderDto.Jobs.First(jdto => jdto.Id == j.Id).Visibility = GetFlagsRelatedToJob(j);
			return new OkObjectResult(orderDto);
		}
		/*
		[HttpPost("{id:int}/o/s")]
		[Produces(typeof(IRun))]
		public IActionResult SetMeAsOperator(int id) //int? Id = null
		{
			var order = _orderApp.GetOrder(id);
			if (order == null) return NotFound();

			if(_currentUser is IStaff)
			{
				var staff = (IStaff)_currentUser;
				if(staff.Role == Role.Prepress)
				{
					order.Operator = staff;
					_orderApp.BaseSave(order);
				}
			}

			return Ok();
		}

		[HttpPost("{id:int}/o/c")]
		[Produces(typeof(IRun))]
		public IActionResult ClearMeAsOperator(int id) //int? Id = null
		{
			var order = _orderApp.GetOrder(id);
			if (order == null) return NotFound();

			if (_currentUser is IStaff)
			{
				var staff = (IStaff)_currentUser;
				if (order.Operator == staff)
				{
					order.Operator = null;
					_orderApp.BaseSave(order);
				}
			}

			return Ok();
		}
		[HttpPost("{id:int}/o/c")]
		[HttpPost("order/{id:int}/o/c")]
		[Produces(typeof(IRun))]
		public IActionResult ClearMeAsOperator(int id) //int? Id = null
		{
			var order = _orderApp.GetOrder(id);
			if (order == null) return NotFound();

			if (_currentUser is IStaff)
			{
				var staff = (IStaff)_currentUser;
				if (order.InUseBy == staff.FirstName)
				{
					order.InUseBy = null;
					_orderApp.BaseSave(order);
				}
			}

			return Ok();
		}


		*/

		[HttpPost("order/{id:int}/o/c/{name}")]
		[AllowAnonymous]
		public IActionResult ClearMeAsOperator(int id, string name) //int? Id = null
		{
			if (!(_currentUser is IStaff))
				return Ok();

			var staff = (IStaff)_currentUser;
			if (_cachedAccessTo.GetOrderInUseBy(id) == name)
			{
				_cachedAccessTo.SetOrderInUseBy(id, string.Empty);
			}
			return Ok();
		}



		[ApiExplorerSettings(IgnoreApi = false)]
		[HttpPost("Order/{orderId:int}/large")]
		[Produces(typeof(IOrder))]
		public IActionResult GetOrderLarge(int orderId)
		{
			var order = _orderApp.GetOrder(orderId);

			if (order == null) return NotFound();

			if (!AccessAllowed(order))
			{
				return Forbid();
			}

			if (_currentUserIsCust && order.IsDeleted)
				return new StatusCodeResult((int)HttpStatusCode.NoContent);

			//if (_currentUser is IStaff)
			//{
			//	var staff = (IStaff)_currentUser;
			//	//order.Operator = staff;
			//	order.InUseBy = staff.FirstName;
			//	_orderApp.BaseSave(order);
			//}

			if (_currentUser is IStaff)
			{
				var staff = (IStaff)_currentUser;
				_cachedAccessTo.SetOrderInUseBy(order.Id, staff.FirstName);
				//order.InUseBy = staff.FirstName;
				//_orderApp.BaseSave(order);
			}


			var promotionApplyLog = _orderApp.ScatteredLogic_AutoPopulatePromotion(order, _currentUser);

			Log.Information("{User} opening Order {OrderId}", _currentUser.Username, orderId);

			var orderDto = _mapper.Map<OrderDtoLarge>(order);
			orderDto.Visibility = GetFlagsRelatedToOrder(order);
			orderDto.Visibility.promotionApplyLog = promotionApplyLog;

			foreach (var j in order.Jobs)
				orderDto.Jobs.First(jdto => jdto.Id == j.Id).Visibility = GetFlagsRelatedToJob(j);

			try
			{
				var fpc = _orderApp.BaseSession.CreateSQLQuery(
								$@"Select Code,Description,Margin from  [CustomerFreightPricing] where Code = '{order.Customer.FreightPriceCode}'")
							.Future<object[]>().FirstOrDefault();
				if (fpc != null)
				{
					orderDto.DescFreightPriceCode = new { Code = fpc[0], Description = fpc[1], Margin = fpc[2] };
				}


				orderDto.DescProductPriceCodes = _orderApp.BaseSession.CreateSQLQuery(
						//$@"SELECT        pp.Code, pp.Description, JobOption.Name AS JobType, ppd.Margin
						//FROM            CustomerProductPricingDetail AS ppd INNER JOIN
						//						 JobOption ON ppd.JobOptionID = JobOption.Id INNER JOIN
						//						 CustomerProductPricing AS pp ON ppd.CustomerPricingID = pp.id
						//WHERE        (ppd.JobOptionID IN (SELECT DISTINCT JobOptionId FROM Job WHERE (OrderId = {order.Id}))) AND (pp.Code IN
						//                        (SELECT        c.ProductPriceCode
						//                          FROM            Customer AS c INNER JOIN
						//                                                    [Order] AS o ON c.CustomerId = o.userId
						//                          WHERE        (o.Id = {order.Id})))"

						$@"SELECT   Distinct     pp.Code, pp.Description, JobOption.Name AS JobType, ppd.Margin, [Order].Id
						FROM            [Order] INNER JOIN
                         Job ON [Order].Id = Job.OrderId AND [Order].Id = Job.OrderId INNER JOIN
                         Customer ON [Order].userId = Customer.CustomerId INNER JOIN
                         CustomerProductPricingDetail AS ppd INNER JOIN
                         JobOption ON ppd.JobOptionID = JobOption.Id INNER JOIN
                         CustomerProductPricing AS pp ON ppd.CustomerPricingID = pp.id ON Customer.ProductPriceCode = pp.Code AND Job.JobOptionId = ppd.JobOptionID
						where [Order].Id =  {order.Id}"

							   )
					.Future<object[]>().Select(_ => new { Code = _[0], Description = _[1], JobType = _[2], Margin = _[3] });

			}
			catch (Exception ex)
			{
				Log.Error(ex, ex.Message);
			}
			return new OkObjectResult(orderDto);
		}

		private OrderPrintDto GetOrderPrintDto(IOrder order)
		{
			var orderDto = _mapper.Map<OrderPrintDto>(order);
			var baseUrl = _config["AbsolutePathURL"];

			foreach (var j in order.Jobs)
			{
				var thumbs = _cachedAccessTo.Thumbnails(j).Take(2).Select(f => f.Name).ToList();

				if (thumbs.Any())
				{
					var jx = orderDto.Jobs.First(jdto => jdto.Id == j.Id);

					jx.Thumb0 = $"{baseUrl}/api/orders/Job/{jx.Id}/thumb/{DateTime.Now.Ticks}/{thumbs[0]}";

					if (thumbs.Count == 2)
					{
						jx.Thumb1 = $"{baseUrl}/api/orders/Job/{jx.Id}/thumb/{DateTime.Now.Ticks}/{thumbs[1]}";
					}
				}
			}
			return orderDto;
		}

		[HttpGet("Order/{orderId:int}/printDto")]
		[HttpPost("Order/{orderId:int}/printDto")]
		[Produces(typeof(OrderPrintDto))]
		[AllowAnonymous]
		public IActionResult GetOrderPrintDto(int orderId)
		{
			var order = _orderApp.GetOrder(orderId);

			if (order == null) return NotFound();

			if (!AccessAllowed(order))
			{
				return Forbid();
			}
			var orderDto = GetOrderPrintDto(order);

			return new OkObjectResult(orderDto);
		}

		[HttpGet("Order/{orderId:int}/quotePdf")]
		[HttpPost("Order/{orderId:int}/quotePdf")]
		[AllowAnonymous]
		public IActionResult GetOrderQuotePdf(int orderId, [FromQuery] bool htmlFormat)
		{
			var order = _orderApp.GetOrder(orderId);

			if (order == null) return NotFound();

			if (!AccessAllowed(order))
			{
				return Forbid();
			}
			var orderDto = GetOrderPrintDto(order);

			var jsonStr = JsonConvert.SerializeObject(orderDto);
			jsonStr = jsonStr.Replace("'", "''");

			Report report = new Report();
			var fileName = _config["Reports:LepQuote"];
			report.Load(fileName);

			report.Dictionary.Connections[0].ConnectionString = "Json='" + jsonStr + "'";
			report.Prepare();


			if (htmlFormat)
			{
				var html = new HTMLExport
				{
					EnableVectorObjects = true,
					EmbedPictures = true,
					SinglePage = true,
					SubFolder = false,
					Layers = true,
					Navigator = false
				};

				using (var ms = new MemoryStream())
				{
					html.Export(report, ms);
					return new FileContentResult(ms.ToArray(), "text/html")
					{
						FileDownloadName = $"{orderDto.Title}-{order.Id}.html"
					};
				}
			}

			var pdfExport = new PDFSimpleExport();
			using (var ms = new MemoryStream())
			{
				pdfExport.Export(report, ms);
				return new FileContentResult(ms.ToArray(), "application/pdf")
				{
					FileDownloadName = $"{orderDto.Title}-{order.Id}.pdf"
				};
			}
		}

		private bool AccessAllowed(IOrder order)
		{
			var thisOrdersCustomerId = order.Customer.Id;

			if (_currentUserIsCust && thisOrdersCustomerId != _currentUser.Id)
			{
				return false;
			}

			if (_currentUserIsCust && order.IsDeleted)
				return false;

			if (_currentUserIsAnonymousWLCustomer) // see Account's controller.WhiteLabelIndex
			{
				if (thisOrdersCustomerId != _currentUser.Id)
					return false;

				//var sessionId = _httpContextAccessor.HttpContext.Request.Headers["SessionId"];

				//if (sessionId != order.WLAnonymousUserId.ToString())
				//	return false;
			}

			if (_currentUserIsLoggedInWLCustomer)
			{
				var thisCustomersParentCustomerId = (_currentUser as ICustomerUser).ParentCustomer.Id;
				if (thisOrdersCustomerId != thisCustomersParentCustomerId)
				{
					return false;
				}
			}

			return true;
		}


		[ApiExplorerSettings(IgnoreApi = false)]
		[HttpPost("Order/{orderId:int}/large2")]
		[Produces(typeof(IOrder))]
		public IActionResult GetOrderLarge2(int orderId)
		{
			var order = _orderApp.GetOrder(orderId);

			if (order == null) return NotFound();

			if (!AccessAllowed(order))
			{
				return Forbid();
			}

			if (_currentUserIsCust && order.IsDeleted)
				return new StatusCodeResult((int)HttpStatusCode.NoContent);


			Log.Information("{User} opening Order {OrderId}", _currentUser.Username, orderId);

			var orderDto = _mapper.Map<OrderDtoLarge2>(order);
			orderDto.Visibility = GetFlagsRelatedToOrder(order);

			foreach (var j in order.Jobs)
				orderDto.Jobs.First(jdto => jdto.Id == j.Id).Visibility = GetFlagsRelatedToJob(j);
			return new OkObjectResult(orderDto);
		}

		[ApiExplorerSettings(IgnoreApi = false)]
		[HttpGet("Order/{orderId:int}/GetAvailableRates")]
		public IActionResult GetAvailableRates([FromRoute] int orderId)
		{
			var order = _orderApp.GetOrder(orderId);
			var facilityFromPostCode = _orderApp.GetProductionFacilityByPostCode(order.DeliveryAddress.Postcode)
									   ?? Facility.FG;
			var rates = _courierApplication.GetAvailableRates(order, facilityFromPostCode, true, false);
			return new OkObjectResult(rates);
		}

		[HttpGet("Order/{orderId:int}/GetAvailableRatesFromFacility/{facility}")]
		public IActionResult GetAvailableRatesFromFacility([FromRoute] int orderId, [FromRoute] Facility facility)
		{
			var order = _orderApp.GetOrder(orderId);
			var rates = _courierApplication.GetAvailableRates(order, facility, true, _currentUserIsStaff);
			return new OkObjectResult(rates);
		}



		[ApiExplorerSettings(IgnoreApi = false)]
		[HttpPost("Order/{orderId:int}/delete")]
		//[ValidateActionParameters]
		public IActionResult DeleteOrder([FromRoute] int orderId)
		{
			try
			{
				var order = _orderApp.GetOrder(orderId);
				if (_currentUserIsStaff)
				{
					_orderApp.Delete(order);
				}
				else
				{
					if (AccessAllowed(order))
					{
						order.IsDeleted = true;
						foreach (var j in order.Jobs)
						{
							if (j.IsQuotePrice)
								j.QuoteOutcome = QuoteStatus.Lost.ToString();
						}
						_orderApp.Save(order);
					}
				}
			}
			catch (Exception ex)
			{
				return new BadRequestObjectResult(new { error = ex });
			}
			return new ContentResult { StatusCode = (int)HttpStatusCode.Accepted, Content = "The Order has been deleted." };
		}

		[HttpPost("Order/{orderId:int}/reorder")]
		//[ValidateActionParameters]
		public IActionResult ReorderOrder([FromRoute] int orderId)
		{
			IOrder order = null;
			IOrder newOrder = null;

			try
			{
				order = _orderApp.GetOrder(orderId);

				if (!_currentUser.IsStaff && order.Customer.Id != _currentUser.Id)
					return new StatusCodeResult(403);

				newOrder = _orderApp.ReorderOrder(order);
				newOrder.PaymentStatus = OrderPaymentStatusOptions.AwaitingPayment;
				_orderApp.Save(newOrder);
			}
			catch (Exception ex)
			{
				return new BadRequestObjectResult(new
				{
					error = ex
				});
			}
			Response.Headers.Add("OrderId", newOrder.Id.ToString());
			return new ContentResult
			{
				StatusCode = (int)HttpStatusCode.Accepted,
				Content = $"A new Order #{newOrder.Id} Order has been created from Order #{order.Id}"
			};
		}

		[HttpPost("Order/{orderId:int}/ReprintRestart")]
		[Produces(typeof(OrderSummaryDtoWithJobs))]
		[ReturnBadRequestOnModelError]
		public IActionResult ReprintOrder([FromRoute] int orderId, [FromBody] OrderReprintRestartDto reprintRestartArgs)
		{
			IOrder order = null;
			IOrder newOrder = null;

			try
			{
				order = _orderApp.GetOrder(orderId);

				if (!_currentUser.IsStaff && order.Customer.Id != _currentUser.Id)
					return new StatusCodeResult(403);

				newOrder = _orderApp.ReprintOrder(_currentUser, order, reprintRestartArgs);

				_orderApp.Save(newOrder);
			}
			catch (Exception ex)
			{
				return new BadRequestObjectResult(new
				{
					error = ex
				});
			}
			Response.Headers.Add("OrderId", newOrder.Id.ToString());
			return new ContentResult
			{
				StatusCode = (int)HttpStatusCode.Accepted,
				Content = $"A new Order #{newOrder.Id} Order has been created from Order #{order.Id}"
			};
		}




		[HttpPost("Order/{orderId:int}/Copy")]
		public IActionResult CopyOrder([FromRoute] int orderId)
		{
			IOrder order = null;
			IOrder newOrder = null;

			try
			{
				order = _orderApp.GetOrder(orderId);

				if (!_currentUser.IsStaff && order.Customer.Id != _currentUser.Id)
					return new StatusCodeResult(403);
				newOrder = _orderApp.CopyOrder2(order);
			}
			catch (Exception ex)
			{
				return new BadRequestObjectResult(new
				{
					error = ex
				});
			}
			Response.Headers.Add("OrderId", newOrder.Id.ToString());
			return new ContentResult
			{
				StatusCode = (int)HttpStatusCode.Accepted,
				Content = $"A Order #{newOrder.Id} Order is a copy of Order #{order.Id}"
			};
		}

		[HttpPost("Order/{orderId:int}/ApproveQuoteAndSubmit")]
		public IActionResult ApproveQuoteAndSubmit(int orderId)
		{
			var order = _orderApp.GetOrder(orderId);
			order.Jobs.Where(_ => _.QuoteNeedApprove).ForEach(_ =>
			{
				_.ApproveQuote(_currentUser);
				_.QuoteOutcome = QuoteStatus.WonPendingSubmitted.ToString();
			});
			_orderApp.Save(order);
			return OrderSubmit(orderId);
		}

		[HttpPost("Order/{orderId:int}/WLOrderPayment")]
		public IActionResult WLOrderPayment([FromRoute] int orderId, [FromBody] dynamic d)
		{
			var order = _orderApp.GetOrder(orderId);

			order.IsWLOrderPaidFor = (bool)d.IsWLOrderPaidFor;
			order.WLOrderPaymentDetails = (string)d.WLOrderPaymentDetails;
			//order.Status = OrderStatusOptions.SubmittedInPortal;
			_orderApp.Save(order);
			_emailApp.SendNotification(order, ContentType.WebOrderRaisedWithPaypalPayment);
			return Ok();
		}

		public class StripeSubmit
		{
			public string stripeToken { get; set; }
			public int amount { get; set; }
		}

		[HttpPost("Order/{orderId:int}/StripePayment")]
		public IActionResult WLStripePayment([FromRoute] int orderId, [FromBody] StripeSubmit ss)
		{
			var order = _orderApp.GetOrder(orderId);
			Stripe.StripeConfiguration.ApiKey = order.Customer.PrintPortalSettings.StripeRestrictedChargeKey;
			var options = new Stripe.ChargeCreateOptions
			{
				Amount = ss.amount,
				Currency = "aud",
				Description = $"Order: {orderId}",
				Source = ss.stripeToken
			};
			var service = new Stripe.ChargeService();
			try
			{
				Stripe.Charge charge = service.Create(options);
				order.IsWLOrderPaidFor = true;
				order.WLOrderPaymentDetails = charge.ReceiptNumber;
				//order.Status = OrderStatusOptions.SubmittedInPortal;
				_orderApp.Save(order);
				_emailApp.SendNotification(order, ContentType.WebOrderRaisedWithStripePayment);
			}
			catch (Exception ex)
			{
				Log.Error(ex, ex.Message);
			}
			return Ok();
		}

		[HttpPost("Order/{orderId:int}/WL/AccountPay")]
		public IActionResult WLAccountPay([FromRoute] int orderId, [FromBody] StripeSubmit ss)
		{
			var order = _orderApp.GetOrder(orderId);
			order.IsWLOrderPaidFor = true;
			order.WLOrderPaymentDetails = "Pay By Account";
			//order.Status = OrderStatusOptions.SubmittedInPortal;
			_orderApp.Save(order);
			_emailApp.SendNotification(order, ContentType.WebOrderRaisedWithPaypalPayment);
			return Ok();
		}

		[HttpPost("Order/{orderId:int}/submit")]
		public IActionResult OrderSubmit(int orderId)
		{
			Log.Information($"{_currentUser.Username} Submiting Order {orderId}");

			try
			{
				var order = _orderApp.GetOrder(orderId);

				//if(!order.Courier.IsPickup && order.PriceOfJobs != null && order.Customer.FreightPriceCode != "F1")
				//{

				//	if( !((_currentUser is IStaff) && (order.Jobs.Any(_ => _.IsReprint))))
				//	{
				//		if (order.Courier.IsNone)
				//		{
				//			return new ContentResult
				//			{
				//				StatusCode = (int)HttpStatusCode.BadRequest,
				//				Content = $"Please refresh the LEP webpage (Ctrl+F5) and select a Freight"
				//			};
				//		}

				//		if(order.PackDetail.Price == null || order.PackDetail.Price == 0)
				//		{
				//			return new ContentResult
				//			{
				//				StatusCode = (int)HttpStatusCode.BadRequest,
				//				Content = $"Please refresh the LEP webpage (Ctrl+F5) and select a Freight"
				//			};
				//		}
				//	}
				//}


				foreach (var job in order.Jobs)
				{
					var isArtworkValidForSubmit = job.IsArtworkValidForSubmit();

					if (order.PriceOfJobs == null && !isArtworkValidForSubmit) break;

					if (!isArtworkValidForSubmit && job.Price != "")
						return new ContentResult
						{
							StatusCode = (int)HttpStatusCode.BadRequest,
							Content = $"Artwork required for job {job.JobNr}"
						};

					if (!job.IsArtworkValidForSubmit())
						return new ContentResult
						{
							StatusCode = (int)HttpStatusCode.BadRequest,
							Content = $"Artwork required for job {job.JobNr}"
						};

					if (job.QuoteNeedApprove)
						return new ContentResult
						{
							StatusCode = (int)HttpStatusCode.BadRequest,
							Content = $"Quote needs approval for job {job.JobNr}"
						};

					if (job.IsQuoteExpired)
						return new ContentResult
						{
							StatusCode = (int)HttpStatusCode.BadRequest,
							Content = $"Quote expired for job {job.JobNr}"
						};
				}

				if (_currentUser is ICustomerUser && _currentUserIsAnonymousWLCustomer
					&& order.Price.HasValue && order.PendingPayment > 0)
				{
					var paymentUrl = "";
					var selectedGateway = _configApp.GetValue(Configuration.ActivePaymentGateway);
					if (selectedGateway == "ANZ") paymentUrl = "";
					else if (selectedGateway == "Westpac") paymentUrl = GetWestpacPaymentUrl(order);
					Response.Headers.Add("Location", paymentUrl);
					return new StatusCodeResult((int)PaymentRequired);
				}

				if (_currentUser is ICustomerUser && order.Customer.PaymentTerms.Is(PrePay, COD))
					if (order.Price.HasValue)
						if (order.PendingPayment > 0)
						{
							var paymentUrl = "";
							var selectedGateway = _configApp.GetValue(Configuration.ActivePaymentGateway);
							if (selectedGateway == "ANZ") paymentUrl = "";
							else if (selectedGateway == "Westpac") paymentUrl = GetWestpacPaymentUrl(order);

							Log.Information("Sending User to Westpac {orderId}", order.Id);
							Response.Headers.Add("Location", paymentUrl);
							return new StatusCodeResult((int)PaymentRequired);
						}
						else if (order.Price == 0)
						{
							order.PaymentStatus = OrderPaymentStatusOptions.Paid;
						}

				if (_currentUser is ICustomerUser && order.Customer.PaymentTerms == OnHold)
					order.PaymentStatus = OrderPaymentStatusOptions.NotNeeded;

				_orderApp.Submit(order, _currentUser);
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
				//return new BadRequestObjectResult(ex.Message);
				return new ContentResult { StatusCode = (int)HttpStatusCode.BadRequest, Content = ex.Message };
			}
			return new ContentResult
			{
				StatusCode = (int)HttpStatusCode.Accepted,
				Content = "The Order/Job has been submitted."
			};
		}

		[HttpPost("Order/AttachConnote")]
		[AllowAnonymous]
		public IActionResult AttachConnote([FromBody] OrderConNoteDto connoteDto)
		{
			try
			{
				var connote = (IOrderConNote)_mapper.Map<lep.order.impl.OrderConNote>(connoteDto);
				_orderApp.AttachConnote(connote);
			}
			catch (Exception ex)
			{
				return new ContentResult { StatusCode = (int)HttpStatusCode.BadRequest, Content = ex.Message };
			}
			return new ContentResult
			{
				StatusCode = (int)OK,
				Content = "Connote saved"
			};
		}

		[HttpPost("Order/{orderId:int}/Credit")]
		[Authorize(Roles = LepRoles.Staff)]
		public IActionResult OrderCredit([FromRoute] int orderId, [FromBody] OrderCredit oc)
		{
			if (oc == null)
				return BadRequest();

			IOrder order = null;

			order = _orderApp.GetOrder(orderId);

			var orderCredit = new OrderCredit()
			{
				Order = order,
				Customer = order.Customer,
				IssuedBy = (IStaff)_currentUser,
				Amount = oc.Amount,
				Description = oc.Description,
				Type = oc.Type,
				Account = oc.Account,
				GST = oc.GST,
				Total = oc.Total,
				DateCreated = oc.DateCreated != default(DateTime) ? oc.DateCreated.Date : DateTime.Now,
				 
				Approved = oc.Approved,
				Invoiced = false,

			};

			order.OrderCredits.Add(orderCredit);
			_orderApp.Save(order);
			return Ok();
		}


		[HttpPost("Customer/{customerId:int}/Credit")]
		[Authorize(Roles = LepRoles.Staff)]
		public IActionResult CustomerCredit([FromRoute] int customerId, [FromBody] OrderCredit oc, [FromServices] NHibernate.ISession session)
		{
			if (oc == null)
				return BadRequest();

			var customer = _userApplication.GetCustomerUser(customerId);

			session.Save(new OrderCredit()
			{
				Order = null,
				Customer = customer,
				IssuedBy = (IStaff)_currentUser,
				Amount = oc.Amount,
				Description = oc.Description,
				Type = oc.Type,
				Account = oc.Account,
				GST = oc.GST,
				Total = oc.Total,
				DateCreated = oc.DateCreated != default(DateTime) ? oc.DateCreated : DateTime.Now,
				 
				Approved = oc.Approved,
				Invoiced = false,

			});

			return Ok();
		}





		public string GetWestpacPaymentUrl(IOrder order)
		{
			var orderId = order.Id.ToString();
			var price = "";
			price = Math.Round(order.PendingPayment.Value, 2, MidpointRounding.AwayFromZero).ToString();

			var customerId = order.Customer.Id.ToString();

			var username = _configApp.GetValue(Configuration.WestpacUsername);
			var password = _configApp.GetValue(Configuration.WestpacPassword);
			var billerCode = _configApp.GetValue(Configuration.WestpacBillercode);
			var merchantId = _configApp.GetValue(Configuration.WestpacMerchantId);
			var payWayUrl = _configApp.GetValue(Configuration.WestpacPaywayUrl);

			var redirectUrl = $"{Request.Scheme}://{Request.Host}/api/Orders/WestPackPayment";
			var redirecPrepayUrl = $"{Request.Scheme}://{Request.Host}/#!/cust/your-order/{orderId}/open";

			//var tokenUtil = new TokenUtil();
			//tokenUtil.Init(username, password, billerCode, merchantId, payWayUrl, orderId, null, 0, null, null);

			//tokenUtil.AddHiddenField("CustomerId", customerId);
			//tokenUtil.AddHiddenField("OrderId", orderId);
			//tokenUtil.AddConfigurationParameter("payment_amount", price);

			//tokenUtil.AddConfigurationParameter("return_link_payment_status", "all");
			//tokenUtil.AddConfigurationParameter("return_link_url", redirectUrl);
			//tokenUtil.AddConfigurationParameter("return_link_text", "back to My LEP");
			//tokenUtil.AddConfigurationParameter("return_link_redirect", "true"); // if you wanna come to LEP Site then set this to true

			//tokenUtil.AddConfigurationParameter("return_link_text_pre_payment", "back to My LEP");
			//tokenUtil.AddConfigurationParameter("return_link_url_pre_payment", redirecPrepayUrl);

			//tokenUtil.AddConfigurationParameter("receipt_address", order.Customer.AccountEmail);
			//tokenUtil.AddConfigurationParameter("payment_reference_change", "false");
			//tokenUtil.AddConfigurationParameter("payment_reference_text", "Pay for LEP Order");
			//tokenUtil.AddConfigurationParameter("payment_reference_text_help", "");

			//var token = tokenUtil.GetToken();


			var p = new List<KeyValuePair<string, string>>();
			Action<string, string> add = (string x, string y) => p.Add(new KeyValuePair<string, string>(x, y));

			add("username", username);
			add("password", password);
			add("biller_code", billerCode);
			add("merchant_id", merchantId);
			add("payment_reference", orderId);
			add("CustomerId", customerId);
			add("OrderId", orderId);
			add("payment_amount", price);
			add("return_link_payment_status", "all");
			add("return_link_url", redirectUrl);
			add("return_link_text", "back to My LEP");
			add("return_link_redirect", "true");

			add("return_link_text_pre_payment", "back to My LEP");
			add("return_link_url_pre_payment", redirecPrepayUrl);
			add("receipt_address", order.Customer.AccountEmail);
			add("payment_reference_change", "false");
			add("payment_reference_text", "Pay for LEP Order");
			add("payment_reference_text_help", "");

			add("hidden_fields", "OrderId,CustomerId");



			var requestContent = new FormUrlEncodedContent(p);

			var response = Program.ClientWestPack.PostAsync(payWayUrl + "RequestToken", requestContent).Result;
			var responseUri = response.Content.ReadAsStringAsync().Result;
			var query = HttpUtility.ParseQueryString(responseUri);

			var token = query.Get("token");

			if (token == null) throw new Exception("Null Token");

			var handOffUrl = payWayUrl + "MakePayment";
			handOffUrl += "?biller_code=" + HttpUtility.UrlEncode(billerCode)
						  + "&token=" + HttpUtility.UrlEncode(token);

			return handOffUrl;
		}

		[HttpGet("WestPackPayment")]
		[AllowAnonymous]
		public IActionResult WestPackPayment([FromServices] NHibernate.ISession session)
		{
			IOrder order = null;
			IDictionary dict = null;
			string encryptedParameters = Request.Query[LEPWestpac.Constants.EncryptedParameters];
			string signature = Request.Query[LEPWestpac.Constants.Signature];
			var westpacEncKey = _configApp.GetValue(Configuration.WestpacEncryptionKey);

			Log.Information("WestPackPayment: {encryptedParameters} {signature}", encryptedParameters, signature);
			try
			{
				dict = LEPWestpac.DecryptParameters(westpacEncKey, encryptedParameters, signature);
			}
			catch (Exception ex)
			{
				Log.Error(ex, ex.Message);
				return BadRequest(ex);
			}

			var strOrderId = (string)dict["payment_reference"];
			var strPaymentNumber = (string)dict["payment_number"];
			var strPaymentStatus = (string)dict["payment_status"];
			var responceCode = (string)dict["response_code"];

			var orderid = 0;
			int.TryParse(strOrderId, out orderid);
			if (orderid > 0) order = _orderApp.GetOrder(orderid);

			if (order == null)
			{
				var msg = $"Order #{strOrderId} does not exist";
				return new ContentResult { StatusCode = (int)HttpStatusCode.BadRequest, Content = msg };
			}
			ICustomerUser customer = order.Customer;

			if (strPaymentStatus == LEPWestpac.Constants.PaymentApproved)
			{
				try
				{
					Login(customer);
					using (var tx = session.BeginTransaction())
					{
						order.PaymentStatus = OrderPaymentStatusOptions.Paid;
						decimal amount = decimal.Parse((string)dict["payment_amount"] ?? "0");
						order.AddPayment(amount);
						_orderApp.Save(order);
						Log.Information("WestPackPayment: Submitting Order #{strOrderId}, amount: {amount}, PaymentNumber: {strPaymentNumber} ", strOrderId, amount, strPaymentNumber);
						_orderApp.Submit(order, order.Customer);
						tx.Commit();
					}

				}
				catch (Exception ex)
				{
					Log.Error("WestPackPayment: Error saving payment");
					return new ContentResult { StatusCode = (int)HttpStatusCode.BadRequest, Content = ex.Message };
				}
			}

			try
			{
				if (strPaymentStatus == LEPWestpac.Constants.PaymentDeclined)
				{
					var message = "";
					message = $"<br/>Your payment was declined!<br/>Order Number: {strOrderId}";
					message += $"<br/><br/><a href=\"{"customer/order-view.aspx?id=" + strOrderId}\">Click here to try again</a>";
					var responceFromCode = LEPWestpac.getResponceFromCode(responceCode);
					var description = LEPWestpac.getDescription(responceCode);
					message +=
						$"<br/><br/>Responce:{responceCode} {responceFromCode} <br/>{description}";
				}
			}
			catch (Exception ex)
			{
				Log.Error("WestPackPayment: " + ex.Message, ex);
			}

			return new RedirectResult($"/#!/cust/your-order/{strOrderId}/open");
		}

		private void Login(ICustomerUser user)
		{
			var authProperties = new Microsoft.AspNetCore.Authentication.AuthenticationProperties();
			var userClaims = new List<Claim>
			{
				new Claim("UserId", user.Id.ToString()),
				new Claim(ClaimTypes.Name, user.Username),
				new Claim(ClaimTypes.Role, LepRoles.Customer),
			};

			var principal = new ClaimsPrincipal(new ClaimsIdentity(userClaims, "Cookies"));
			HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, principal, authProperties);
			_httpContextAccessor.HttpContext.Items["CurrentUser"] = user;
		}

		[HttpPost("Order/{orderId:int}/submitReady")]
		public IActionResult OrderSubmitReady(int orderId)
		{
			try
			{
				var order = _orderApp.GetOrder(orderId);
				_orderApp.SubmitReady(order, (IStaff)_currentUser);
			}
			catch (Exception ex)
			{
				return new ContentResult { StatusCode = (int)HttpStatusCode.BadRequest, Content = ex.Message };
			}
			return new ContentResult
			{
				StatusCode = (int)HttpStatusCode.Accepted,
				Content = "Your changes have been successfully saved."
			};
		}

		[HttpPost("Order/{orderId:int}/withdraw")]
		public IActionResult OrderWithdraw(int orderId)
		{
			var order = _orderApp.GetOrder(orderId);
			if (order.Withdraw(_currentUser))
			{
				try
				{
					_orderApp.Save(order);
				}
				catch (Exception ex)
				{
					return new ContentResult { StatusCode = (int)HttpStatusCode.BadRequest, Content = ex.Message };
				}

				return new ContentResult
				{
					StatusCode = (int)HttpStatusCode.Accepted,
					Content = "The Order/Job has been withdrawn."
				};
			}
			else
			{
				return new ContentResult
				{
					StatusCode = (int)PreconditionFailed,
					Content = "The Order/Job can’t been automatically withdrawn, please phone LEP Colour Printer."
				};
			}
		}

		/*
		[HttpPost("Order/{orderId:int}/reactivate")]
		public IActionResult OrderReactivate (int orderId)
		{
			var order = _orderApp.GetOrder(orderId);
			if (order.Reactivate(_currentUser)) {
				_orderApp.Save(order);
				return new ContentResult {
					StatusCode = (int)HttpStatusCode.Accepted,
					Content = "The Order/Job has been reactivated."
				};
			} else {
				return new ContentResult {
					StatusCode = (int)HttpStatusCode.PreconditionFailed,
					Content = "The Order/Job can’t been automatically reactivated, please phone LEP Printed Printer."
				};
			}
		}*/

		[HttpPost("Order/{orderId:int}/return")]
		public IActionResult OrderReturn(int orderId)
		{
			var order = _orderApp.GetOrder(orderId);
			_orderApp.ReturnOrder(order);
			return new ContentResult
			{
				StatusCode = (int)HttpStatusCode.Accepted,
				Content = "Your successfully returned to customer."
			};
		}

		[HttpPost("Order/{orderId:int}/resetAddress")]
		public IActionResult OrderResetAddress(int orderId)
		{
			var order = _orderApp.GetOrder(orderId);
			order.DeliveryAddress = order.Customer.PostalAddress;
			_orderApp.Save(order);
			return new ContentResult
			{
				StatusCode = (int)HttpStatusCode.Accepted,
				Content = "Order's delivery address has been reset to customers postal adderss"
			};
		}

		[HttpPost("Order/{orderId:int}/applyPromotion")]
		public IActionResult OrderApplyPromotion([FromRoute] int orderId, [FromBody] string promoCode)
		{
			var order = _orderApp.GetOrder(orderId);
			var promoResultLrlText = "";
			var sb = new StringBuilder();

			if (_promotionApplication.ApplyPromotionToOrder(promoCode, order, sb,
				_currentUser is IStaff &&
				((((IStaff)_currentUser).Role == Role.Administrator) || ((IStaff)_currentUser).Role == Role.SuperAdministrator)))
			{
				promoResultLrlText = "Valid Promotion<br/>";
				promoResultLrlText += sb;
				return new ContentResult { StatusCode = (int)HttpStatusCode.Accepted, Content = promoResultLrlText };
			}
			else
			{
				promoResultLrlText = "Invalid Promotion<br/>";
				promoResultLrlText += sb;
				return new ContentResult { StatusCode = (int)PreconditionFailed, Content = promoResultLrlText };
			}
		}

		[HttpPost("NewOrder")]
		[Produces(typeof(OrderSummaryDto))]
		public IActionResult NewOrder([FromBody] int custId)
		{
			var user = _userApplication.GetCustomerUser(custId);
			var order = _orderApp.NewOrder(user);
			_orderApp.Save(order);
			_orderApp.Refresh(order);

			var orderDto = _mapper.Map<OrderSummaryDto>(order);
			return new OkObjectResult(orderDto);
		}

		[HttpGet("Job/{jobId:int}/comments")]
		[Produces(typeof(IList<CommentDto>))]
		public IActionResult GetJobComments(int jobId)
		{
			var job = _jobApp.GetJob(jobId);

			var comments = job.Comments;

			if (_currentUserIsCust)
				comments = comments.Where(_ => !_.LepOnly).ToList();

			var result = comments.Select(c => _mapper.Map<CommentDto>(c)).ToList();

			return new OkObjectResult(result);
		}

		[HttpPost("Job/{jobId:int}/comments")]
		public IActionResult AddJobComments([FromRoute] int jobId, [FromBody] CommentDto comment)
		{
			var job = _jobApp.GetJob(jobId);
			job.AddComment(_currentUser, comment.CommentText, comment.LepOnly);

			_jobApp.Save(job);
			return Ok();
		}

		[HttpPost("Job/{jobId:int}/commentsAac/")]
		public IActionResult AddJobCommentsWithAAC([FromRoute] int jobId, [FromBody] dynamic c, [FromServices] NHibernate.ISession session1)
		{
			//var job = _jobApp.GetJob(jobId);
			//job = _jobApp.Refresh(job);

			//job.AddComment(_currentUser, (string)c.CommentText, (bool)c.LepOnly);
			//job.AACNotPerformed = c.AACNotPerformed;
			//_jobApp.SaveAndRefreshJob(job);

			var comment = (string)c.CommentText;
			comment = comment.Replace("'", "''");
			var leponly = (bool)c.LepOnly ? "Y" : "N";
			var aacNotPerformed = (bool)c.AACNotPerformed ? "Y" : "N";

			var sql1 = $@"INSERT INTO [dbo].[Comment] ([CommentText],[Author],[JobId],[DateCreated],[LepOnly],[pc])
									 VALUES
										   ('{comment}'
										   , {_currentUser.Id}
										   , {jobId}
										   , GETDATE()
										   , '{leponly}'
										   , 0)";
			//var sql2 = $"Update Job set AACNotPerformed = '{aacNotPerformed}' where id = {jobId}";
			session1.CreateSQLQuery(sql1).ExecuteUpdate();
			//session1.CreateSQLQuery(sql2).ExecuteUpdate();
			return Ok();
		}

		[HttpGet("Job/{jobId:int}/Print/{label}")]
		public IActionResult Print([FromRoute] int jobId, [FromRoute] LabelType? label, [FromServices] PrintEngine printEngine)
		{
			if (label == null) return BadRequest();

			var job = _jobApp.GetJob(jobId);
			if (job == null) return BadRequest();

			_labelPrintApp.Print((LabelType)label, job);
			printEngine.ReceivePrintQueue();
			return Ok();
		}

		[ApiExplorerSettings(IgnoreApi = false)]
		[HttpGet("Job/blank")]
		[Produces(typeof(IJob))]
		public IActionResult NewJob([FromQuery] int? orderId)
		{
			IOrder order = null;
			IJob job = null;
			bool sendSamples = false;
			if (_currentUser is ICustomerUser)
			{
				var cu = (ICustomerUser)_currentUser;

				if (cu.ParentCustomer == null)
				{
					order = _orderApp.NewOrder(cu);
					sendSamples = cu.SendSamples;
				}
				else
				{
					order = _orderApp.NewOrder(cu.ParentCustomer);
				}
			}
			else order = _orderApp.NewOrder();

			if (orderId != null)
			{
				order = _orderApp.GetOrder((int)orderId);
			}

			job = order.NewJob(string.Empty, 0, false, string.Empty, new JobTemplate(), _currentUser);

			var jobDto = _mapper.Map<JobViewCustDto>(job);
			jobDto.FrontPrinting = JobPrintOptions.Printed;
			jobDto.BackPrinting = JobPrintOptions.Printed;
			jobDto.Visibility = GetFlagsRelatedToJob(job);
			jobDto.SendSamples = sendSamples;
			return new OkObjectResult(jobDto);
		}


		[ApiExplorerSettings(IgnoreApi = false)]
		[HttpGet("Job/{jobId:int}")]
		[Produces(typeof(IJob))]
		public IActionResult GetJob(int jobId)
		{
			var job = _jobApp.GetJob(jobId);

			if (job == null)
				return NoContent();

			if (!AccessAllowed(job.Order))
			{
				return Forbid();
			}


			Log.Information("{User} opening job {JobId} ", _currentUser.Username, jobId);
			try
			{
				var jobDto = _mapper.Map<JobViewCustDto>(job);
				jobDto.Visibility = GetFlagsRelatedToJob(job);

				if (!_currentUser.IsStaff)
				{
					jobDto.Comments.RemoveAll(_ => _.LepOnly);
				}

				return new OkObjectResult(jobDto);
			}
			catch (Exception ex)
			{
				return new BadRequestObjectResult(ex.Message);
			}
		}

		[HttpGet("Job/{jobId:int}/RoutingChain")]
		[Produces(typeof(IJob))]
		public IActionResult GetJobRoutingChain([FromRoute] int jobId)
		{
			var job = _jobApp.GetJob(jobId);

			if (job == null)
				return NoContent();

			if (!_currentUser.IsStaff && job.Order.Customer.Id != _currentUser.Id)
				return new StatusCodeResult(403);

			try
			{
				var routesAll = StandardRouting.Instance.GetAllRoutesForJob(job);
				//var routesAllList = routesAll.Select(x => x.ToString()).ToArray();
				//var routingChainTxt = string.Join(",", routesAllList);

				//var routesLeft = StandardRouting.Instance.GetNextRoutesForJob(job).Where(x => x > job.Status);
				//var routesLeftList = routesLeft.Select(x => x.ToString()).ToArray();
				//var countOfAllSteps = routesAll.Count();
				//var countOfStepsDone = routesAll.Count(x => x <= job.Status);
				//var progress = (int)((countOfStepsDone / (float)countOfAllSteps) * 100);
				//var healthCss = _jobBoardDTOHelper.GetJobHealthCSS(job.Id);
				return new OkObjectResult(new
				{
					routesAllList = routesAll,
					//routesLeftList = routesLeftList,
					//countOfAllSteps = countOfAllSteps,
					//countOfStepsDone = countOfStepsDone,
					//progress = progress,
					//healthCss = healthCss
				});
			}
			catch (Exception ex)
			{
				return new BadRequestObjectResult(ex.Message);
			}
		}


		[ApiExplorerSettings(IgnoreApi = false)]
		[HttpPost("Order/{orderId:int}/Job/{jobId:int}")]
		[Produces(typeof(OrderSummaryDtoWithJobs))]
		[DisableRequestSizeLimit]
		public IActionResult SaveJob([FromRoute] int orderId, [FromRoute] int jobId)
		{
			#region SAVE JOB JSON

			//Log.Information("{User} saving job {JobId} ", _currentUser.Username, jobId);
			Log.Information($"{_currentUser.Username}  saving  job  {orderId} - {jobId} ");
			//var s = JsonConvert.SerializeObject(request);
			JobViewCustDto request;
			try
			{
				request = JsonConvert.DeserializeObject<JobViewCustDto>(Request.Form["request"]);
			}
			catch (Exception ex)
			{
				Log.Error(ex, ex.Message);
				Log.Error(Request.Form["request"]);
				throw;
			}

			IOrder order = null;
			IJobTemplate templateR = null;
			IJob job = null;

			ICustomerUser customer = null;

			var idField = "";
			if (_currentUserIsCust)
			{
				idField = "UserId";
			}
			if (_currentUserIsAnonymousWLCustomer)
			{
				idField = "UserId";
			}
			if (_currentUserIsLoggedInWLCustomer)
			{
				idField = "ParentId";
			}
			var mainCustId = User.Claims.Where(c => c.Type == idField).Select(c => c.Value).FirstOrDefault();
			customer = (ICustomerUser)_userApplication.GetUser(Convert.ToInt32(mainCustId));

			if (orderId == 0)
			{
				order = _orderApp.NewOrder(customer);
				order.DeliveryAddress = customer.PostalAddress;

				if (request.Id == 0 &&
					!string.IsNullOrEmpty(request.DeliveryAddress.Postcode) &&
					!string.IsNullOrEmpty(request.DeliveryAddress.City))
					order.DeliveryAddress = request.DeliveryAddress;

				if (_currentUserIsAnonymousWLCustomer)
				{
					order.WLAnonymousUserId = request.WLAnonymousUserId;   // save the anonymous users sessin Id
					order.DeliveryAddress = new PhysicalAddress();         // we dont know this until they provide this
					order.WLContact = new Contact();
					order.PaymentStatus = OrderPaymentStatusOptions.AwaitingPayment;
					order.FreightPriceCode = "";
					order.IsWLOrder = true;
				}
				if (_currentUserIsLoggedInWLCustomer)
				{
					var subCustId = User.Claims.Where(c => c.Type == "SubCustId").Select(c => Int32.Parse(c.Value)).FirstOrDefault();
					var subCust = _userApplication.GetCustomerUser(subCustId);
					order.WLCustomerId = subCustId;
					order.DeliveryAddress = subCust.PostalAddress;

					if (subCust.Contact1 != null)
					{
						var c1 = subCust.Contact1;
						order.WLContact = c1;
						order.RecipientName = c1.Name ?? "";
						order.RecipientPhone = (c1.AreaCode ?? "") + (c1.Phone ?? "");
					}

					order.PaymentStatus = OrderPaymentStatusOptions.AwaitingPayment;
					order.FreightPriceCode = "";
					order.IsWLOrder = true;
				}
			}
			else
			{
				order = _orderApp.GetOrder(orderId);
				customer = order.Customer;

				if (!AccessAllowed(order))
				{
					return Forbid();
				}
			}

			templateR = _jobApp.GetJobTemplate(request.Template.Id);
			if (jobId == 0)
			{
				// new job can only be saved under an order if the order is open
				if (order.Status != OrderStatusOptions.Open)
				{
					return BadRequest("Can not add a new job to this order");
				}

				job = order.NewJob(string.Empty, request.Quantity, false, string.Empty, templateR, _currentUser);
			}

			else
				job = _jobApp.GetJob(jobId);

			job.Name = request.Name;

			if (job.Template != null && job.Template.Id != templateR.Id)
				job.AddComment(_currentUser, "Job Type Changed to " + templateR.Name);

			if (request.ArtworkStatus != ArtworkStatusOption.None) job.ArtworkStatus = request.ArtworkStatus;

			job.TrackProgress = true;
			job.Template = templateR;
			job.Quantity = request.Quantity;
			job.Stock = _jobApp.GetStock(request.Stock.Id);

			if (_currentUserIsStaff)
			{
				if (request.StockOverride != null && request.StockOverride.Id != 0)
					job.StockOverride = _jobApp.GetStock(request.StockOverride.Id);
				else
					job.StockOverride = null;


				if( request.FrontCelloglazeOverride != null)
					job.FrontCelloglazeOverride = request.FrontCelloglazeOverride;
				else
					job.FrontCelloglazeOverride = null;

				if( request.BackCelloglazeOverride != null)
					job.BackCelloglazeOverride = request.BackCelloglazeOverride;
				else
					job.BackCelloglazeOverride = null;
			}



			// if customer and job status is open assign print type
			if (job.IsOpenish())
			{
				job.PrintType = request.PrintType;
				job.NextStatus = StandardRouting.Instance.GetNextRouteForJob(job);
			}
			else if (_currentUser is IStaff &&
				job.PrintType != request.PrintType &&
				job.Status >= JobStatusOptions.Submitted &&
				job.Status < JobStatusOptions.LayoutDone)
			{
				job.AddComment(_currentUser, $"PrintType changed from {job.PrintType} to {request.PrintType}", true);
				job.PrintType = request.PrintType;
				job.NextStatus = StandardRouting.Instance.GetNextRouteForJob(job);
				job.ForcedPrintType = request.PrintType;
				// offset -> digital
				if (request.PrintType == PrintType.D)
				{
					if (order.Status == OrderStatusOptions.Ready)
						//job.Status = JobStatusOptions.DPCPreProduction;
						job.Status = JobStatusOptions.PreflightDone;
					else if (order.Status == OrderStatusOptions.Submitted)
						job.Status = JobStatusOptions.Submitted;
					foreach (var r in job.Runs)
					{
						if (r.Jobs.Contains(job))
						{
							r.Jobs.Remove(job);
						}
					}
					job.Runs.Clear();
				}
				// offset <--- digital
				if (request.PrintType == PrintType.O)
				{
					if (order.Status == OrderStatusOptions.Ready)
						job.Status = JobStatusOptions.PreflightDone;
					else if (order.Status == OrderStatusOptions.Submitted)
						job.Status = JobStatusOptions.Submitted;
				}
			}

			job.FrontPrinting = request.FrontPrinting;
			job.BackPrinting = request.BackPrinting;
			job.FrontCelloglaze = request.FrontCelloglaze;
			job.BackCelloglaze = request.BackCelloglaze;

			job.FrontCelloglazeOverride = request.FrontCelloglazeOverride;
			job.BackCelloglazeOverride = request.BackCelloglazeOverride;

			if (job.FinalFrontCelloglaze.Is(JobCelloglazeOptions.Foil, JobCelloglazeOptions.EmbossFoil))
			{
				job.FoilColour = request.FoilColour;
			}
			else
			{
				job.FoilColour = null;
			}
			job.Envelope = request.Envelope;
			job.EnvelopeType = request.EnvelopeType;

			job.NumberOfMagnets = request.NumberOfMagnets;
			job.Perforating = request.Perforating;
			job.Scoring = request.Scoring;
			job.Rotation = request.Rotation;
			job.BoundEdge = request.BoundEdge;
			job.FinishedSize.PaperSize = _jobApp.GetPaperSize(request.FinishedSize.PaperSize.Id);
			if (job.FinishedSize.PaperSize.Name == "Custom")
			{
				job.FinishedSize.Height = request.FinishedSize.Height;
				job.FinishedSize.Width = request.FinishedSize.Width;
				job.FinishedSize.PaperSize.Size.Width = request.FinishedSize.Width;
				job.FinishedSize.PaperSize.Size.Height = request.FinishedSize.Height;
			}
			else
			{
				//job.FinishedSize.Height = job.FinishedSize.PaperSize.Size.Height;
				//job.FinishedSize.Width = job.FinishedSize.PaperSize.Size.Width;
				job.FinishedSize.Height = job.Rotation == RotationOption.Portrait
					? job.FinishedSize.PaperSize.Size.Height
					: job.FinishedSize.PaperSize.Size.Width;
				job.FinishedSize.Width = job.Rotation == RotationOption.Portrait
					? job.FinishedSize.PaperSize.Size.Width
					: job.FinishedSize.PaperSize.Size.Height;
			}

			if (request.FoldedSize != null && request.FoldedSize.PaperSize != null && request.FoldedSize.PaperSize.Id != 0)
			{
				job.FoldedSize = new Size();
				job.FoldedSize.PaperSize = _jobApp.GetPaperSize(request.FoldedSize.PaperSize.Id);

				if (job.FoldedSize.PaperSize.Name == "Custom")
				{
					job.FoldedSize.Height = request.FoldedSize.Height;
					job.FoldedSize.Width = request.FoldedSize.Width;

					job.FoldedSize.PaperSize.Size.Width = request.FoldedSize.Width;
					job.FoldedSize.PaperSize.Size.Height = request.FoldedSize.Height;
				}
				else
				{
					job.FoldedSize.Height = job.FoldedSize.PaperSize.Size.Height;
					job.FoldedSize.Width = job.FoldedSize.PaperSize.Size.Width;
				}
			}
			else
			{
				job.FoldedSize = null;
			}
			job.RoundOption = request.RoundOption;
			job.RoundDetailOption = request.RoundDetailOption;
			job.TLround = request.TLround;
			job.TRround = request.TRround;
			job.BLround = request.BLround;
			job.BRround = request.BRround;
			job.CustomDieCut = request.CustomDieCut;
			if (job.Template.Is(PresentationFolder, PresentationFolderNDD, TentCalendars))
			{
				job.DieCutType = request.DieCutType; // to do
			}
			else if (job.Template.Is(BusinessCard, BusinessCardNdd, BusinessCardNdd, Postcard))
			{
				job.RoundOption = request.RoundOption;
				job.HoleDrilling = request.HoleDrilling;
			}
			else if (job.IsMagazine())
			{
				job.Pages = request.Pages;
				if (request.BindingOption != null && request.BindingOption.Id != 0)
					job.BindingOption = _jobApp.GetBindingOption(request.BindingOption.Id);

				if (job.Template.Is(MagazineSeparate, A4CalendarSeparateCover))
				{
					if (request.StockForCover.Id != 0) job.StockForCover = _jobApp.GetStock(request.StockForCover.Id);
					job.SelfCovered = false;

					if (_currentUserIsStaff)
					{
						if (request.StockForCoverOverride != null && request.StockForCoverOverride.Id != 0)
							job.StockForCoverOverride = _jobApp.GetStock(request.StockForCoverOverride.Id);
						else
							job.StockForCoverOverride = null;
					}

				}
				else
				{
					job.SelfCovered = true;
				}
			}
			else if (job.Template.Is(WiroMagazines) && job.WiroInfo != null)
			{
				job.Pages = request.Pages;
				if (request.BindingOption != null && request.BindingOption.Id != 0)
					job.BindingOption = _jobApp.GetBindingOption(request.BindingOption.Id);

				//if (request.StockForCover?.Id != 0)
				//	job.StockForCover = _jobApp.GetStock(request.StockForCover.Id);

				if (request.WiroInfo.InnerFrontStockForCover != null)
				{
					job.WiroInfo.InnerFrontStockForCover = (Stock)_jobApp.GetStock(request.WiroInfo.InnerFrontStockForCover.Id);
				}

				if (request.WiroInfo.InnerBackStockForCover != null)
				{
					job.WiroInfo.InnerBackStockForCover = (Stock)_jobApp.GetStock(request.WiroInfo.InnerBackStockForCover.Id);
				}// and the cello properites
				job.WiroInfo.InnerFrontCello = request.WiroInfo.InnerFrontCello;
				job.WiroInfo.InnerBackCello = request.WiroInfo.InnerBackCello;

				// Handle other WiroMagazineInfo properties
				job.WiroInfo.OuterFront = request.WiroInfo.OuterFront;
				job.WiroInfo.OuterBack = request.WiroInfo.OuterBack;
				job.WiroInfo.WireColor = request.WiroInfo.WireColor;


				job.SelfCovered = false;

				//if (_currentUserIsStaff)
				//{
				//	if (request.WiroInfo.StockForCoverBackOverride != null && request.WiroInfo.StockForCoverBackOverride.Id != 0)
				//		job.WiroInfo.StockForCoverBackOverride = (Stock) _jobApp.GetStock(request.WiroInfo.StockForCoverBackOverride.Id);
				//	else
				//		job.StockForCoverOverride = null;
				//}
			}

			if (job.Template.Is(Notepads))
			{
				job.Pages = request.Pages;
			}

			if (job.Template.Is(DuplicateNCRBooks, TriplicateNCRBooks, QuadruplicateNCRBooks))
			{
				job.Pages = request.Pages;
				job.NCRNumbered = request.NCRNumbered;
				job.NCRStartingNumber = request.NCRStartingNumber;
				job.NCRInfo = request.NCRInfo;
			}


			job.NumberOfHoles = request.NumberOfHoles;
			if (job.IsMagazine())
				job.HoleDrilling = request.HoleDrilling;

			if (job.Template.Id == (int)MagazineNDD) job.HoleDrilling = HoleDrilling.None;
			if (job.Template.Id == (int)Notepads) job.PadDirection = request.PadDirection;

			if (string.IsNullOrEmpty(request.SpecialInstructions))
			{
				request.SpecialInstructions = "";
			}
			if (job.SpecialInstructions != request.SpecialInstructions)
			{
				job.AddComment(_currentUser, "Previous Special Instructions:\n" +
					job.SpecialInstructions + "\n\nChanged to:\n" + request.SpecialInstructions);
				job.SpecialInstructions = request.SpecialInstructions;
			}

			job.SendSamples = request.SendSamples;
			job.BrochureDistPackInfo = request.BrochureDistPackInfo;

			var jd = _packageApplication.GetSingleJobThicknessWeight(job);
			job.ThicknessOfSingleJob = jd.depthOf1Job;
			//job.Save Bundle size aswell?   = _packageApplication.GetInBundlesOf(job);


			job.NumberOfMagnets = request.NumberOfMagnets;


			if (_currentUser is IStaff)
			{
				if (request.RequiredByDate == null) job.RequiredByDate = null;
				else job.RequiredByDate = request.RequiredByDate.Value.ToLocalTime();


				if (request.PrintByDate == null) job.PrintByDate = null;
				else job.PrintByDate = request.PrintByDate.Value.ToLocalTime();

				//job.MYOB = request.MYOB;
				job.Urgent = request.Urgent;

				if (string.IsNullOrEmpty(request.ProductionInstructions))
				{
					request.ProductionInstructions = "";
				}
				if (job.ProductionInstructions != request.ProductionInstructions)
				{
					var commentTxt = "Previous Production Instructions:\n" + job.ProductionInstructions + "\n\nChanged to:\n" +
							 request.ProductionInstructions;
					job.AddComment(_currentUser, commentTxt, true);
					job.ProductionInstructions = request.ProductionInstructions;
				}

				if (!request.PreviewChk) job.ReadyArtworkApproval = JobApprovalOptions.NotNeeded;
				else job.ReadyArtworkApproval = JobApprovalOptions.NeedsApproval;
				job.Proofs.ProofsRequired = request.ProofChk;

				var previousProofStatus = job.ProofStatus;
				job.ProofStatus = request.OnHoldChk ? JobProofStatus.OnHold : JobProofStatus.None;

				// if job on hold status has changed update dispatch estimate
				if (previousProofStatus != job.ProofStatus)
				{
					_orderApp.UpdateDispatchDate(order, DateTime.Now);
				}

				job.QuoteNeedApprove = request.QuoteNeedApprove;
				job.HasFacilityChange = job.Facility != null && job.Facility != request.Facility;
				job.IsCustomFacility = request.IsCustomFacility;
				if (request.Facility != null)
					job.Facility = request.Facility;

				if (job.InvolvesOutwork != request.InvolvesOutwork)
				{
					job.InvolvesOutwork = request.InvolvesOutwork;
					job.AddComment(_currentUser, $"flagged Job Involves outwork to {request.InvolvesOutwork}", true);
				}


				job.QuoteEstimator = request.QuoteEstimator;
				job.QuoteCOGS = request.QuoteCOGS;
				job.QuoteOutworkCost = request.QuoteOutworkCost;
				job.QuoteComments = request.QuoteComments;
				job.QuotePrimary = request.QuotePrimary;

				job.QuoteOutcome = request.QuoteOutcome;

				if (job.QuoteOutcome == "Lost")
				{
					job.QuoteLostReason = request.QuoteLostReason;
					job.QuoteLostComments = request.QuoteLostComments;
				}

				if (!string.IsNullOrEmpty(request.QuoteFollowUpNotesAdd))
					job.QuoteFollowUpNotes += $"\n {_currentUser.FirstName} {_currentUser.LastName} @{DateTime.Now.ToLocalTime()}\n{request.QuoteFollowUpNotesAdd}";

			}

			job.IsWhiteLabel = request.IsWhiteLabel;

			if (request.WLCustomerId != null)
			{
				job.Order.WLCustomerId = request.WLCustomerId;
			}

			if ((job.Template.Id == (int)BusinessCard ||
				job.Template.Id == (int)BusinessCardNdd ||
				job.Template.Id == (int)BusinessCardNdd ||
				job.Template.Id == (int)Postcard))
			{
				job.CustomSlot = job.Id == 0 ? job.CalculateJobSize() : request.CustomSlot;
			}

			var sblog = new StringBuilder();
			var productpriceCode = "";
			var freightPriceCode = "";
			decimal freightPriceMargin = 0;

			productpriceCode = customer.ProductPriceCode;
			freightPriceCode = customer.FreightPriceCode;
			freightPriceMargin = _freightApplication.GetCustomerFreightMarginFromCode(freightPriceCode);
			if ((freightPriceCode != "") & (freightPriceMargin != 0))
				sblog.AppendLine($"Freight price code {freightPriceCode} with margin {freightPriceMargin}% applied to total cost");

			//if (!job.IsCustomFacility) {
			//    var facility = _configApp.GetProductionFacilityByPostCode(order.DeliveryAddress.Postcode);
			//    if (_jobApp.IsFacilityAvailable(facility.Value, job.PrintType, job.Template, job.Stock))
			//        job.Facility = facility.Value;
			//    else job.Facility = Facility.FG;
			//}

			//if (job.Freight.IsCustom) {
			//    _freightApplication.SetFreight(job);
			//}
			if (!job.IsReprint)
			{
				var modQty = request.Quantity;
				// if job is a reprint dont mess the pricing else
				//price related function
				decimal enginePrice = 0;
				enginePrice = _pricingEngine.PriceJob(job, sblog, out modQty, productpriceCode);
				//job.MYOB = _pricingEngine.FindMYOB(job, modQty);

				//job.SetPrice(CurrentUser, engineprice, txtprice, quoteApproveChk.Checked);
				decimal txtprice = 0;
				decimal.TryParse(request.Price ?? "", out txtprice);
				job.SetPrice(_currentUser, enginePrice, txtprice, !request.QuoteNeedApprove);

				if (_currentUser is IStaff)
				{
					job.Quantity = request.Quantity;
				}
				else
				{
					//if (enginePrice != 0) job.Quantity = modQty;
					//if (enginePrice != 0) job.Quantity = modQty; // bug fix stop changing quantity
				}

				#region productionnotes

				if (job.Id == 0)
					if (_currentUser is ICustomerUser)
					{
						if (!string.IsNullOrEmpty(((ICustomerUser)_currentUser).ProductionNotes))
							job.ProductionInstructions = ((ICustomerUser)_currentUser).ProductionNotes;
					}
					else
					{
						if (!string.IsNullOrEmpty(job.Order.Customer.ProductionNotes))
							job.ProductionInstructions = job.Order.Customer.ProductionNotes;
					}

				#endregion productionnotes
			}

			//if (_currentUser is IStaff) {
			//	var artworkNeedsReadyApproval = request.PreviewChk && job.Artworks.Any(a => !string.IsNullOrEmpty(a.Ready));
			//	job.JobSaveState(_currentUser, artworkNeedsReadyApproval);
			//}
			if (jobId == 0 && request.Copies > 1)
			{
				job.Name = request.Name + " (" + 1 + ")";
				for (var i = 2; i <= request.Copies; i++)
				{
					var copy = job.CopyJob();
					copy.Name = request.Name + " (" + i + ")";
					copy.Quantity = job.Quantity;
					copy.Price = job.Price;
					copy.PriceBase = job.PriceBase;
					copy.PriceDate = job.PriceDate;

					copy.PriceWL = request.PriceWL;
					order.Jobs.Add(copy);
					//copy.JobSaveState(_currentUser, false);
				}
			}

			foreach (var comment in request.CommentsToAdd)
			{
				job.AddComment(_currentUser, comment.CommentText, false);
			}


			job.PriceWL = request.PriceWL;

			// LORD-1307
			try
			{
				if (string.IsNullOrEmpty(request.Price) || request.Price == "0")
				{
					if (request.CustomerRequestedPrice != null && request.CustomerRequestedPrice != 0)
					{
						job.CustomerRequestedPrice = request.CustomerRequestedPrice;
						job.AddComment(_currentUser, $"Customer requested price {request.CustomerRequestedPrice}", false);
					}
					else
					{
						Log.Error($"Customer requested price 0 {job.Id}");
					}
				}
			}
			catch { }

			_orderApp.Save(order);

			if (jobId == 0)
			{
				_jobApp.CreateArtworkScript(job);
			}

			#endregion SAVE JOB JSON

			var ci = request.AttachmentsToProcess;
			var aacPerformedList = request.AttachmentsToProcess.Select(_ => _.AACPerformed).ToList();
			// this block will be deprecated
			if (Request.Form.Files.Count > 0) ProcessJobUploads(job, Request.Form.Files, request.UploadType, aacPerformedList, _currentUser);
			var baseFolder = _config["DataDirectory"];

			//if (request.AttachmentsToProcess.Any())
			//{
			//	var fxx = new List<MemoryFileHttpPostedFileBase>();

			//	var baseFolder2 = _config["DataDirectory"];

			//	foreach (var attchment in request.AttachmentsToProcess)
			//	{
			//		var position = attchment.Name;
			//		//ContentDispositionHeaderValue.Parse(file.ContentDisposition).FileName.Trim('"');
			//		var attchmentPath = Path.Combine(baseFolder2, "TempUploads", position.ToString());

			#region process job attachments

			if (request.AttachmentsToProcess.Any())

				try
				{
					var files = request.AttachmentsToProcess;


					var cleanList = new List<FileStream>();
					var ffc = new FormFileCollection();
					foreach (var f in files)
					{
						var fullPathInTemp = Path.Combine(baseFolder, "TempUploads", f.Id.ToString());

						try
						{
							//--------------------------------------------------------------------------
							// Save original file uploaded by customer in 'from-customer' folder
							var folderName = LepGlobal.Instance.ArtworkPath(order, job, true, "");
							if (!Directory.Exists(folderName))
							{
								Directory.CreateDirectory(folderName);
							}

							var sourceFilePath = Path.Combine(baseFolder, "TempUploads", f.Name.ToString());
							var destinationFilePath = LepGlobal.Instance.ArtworkPath(order, job, true, f.Name);

							if (System.IO.File.Exists(destinationFilePath))
							{
								// Delete the existing file
								System.IO.File.Delete(destinationFilePath);
							}

							// Copy the file to the destination
							System.IO.File.Copy(sourceFilePath, destinationFilePath);
							//--------------------------------------------------------------------------


							var ifs = new FileStream(fullPathInTemp, FileMode.Open);
							cleanList.Add(ifs); // add these to the list so can be released later
							var ff = new FormFile(ifs, 0, ifs.Length, f.Position, f.Name);
							ffc.Add(ff);

						}
						catch (Exception ex)
						{
							// log
							Log.Error("can not process file " + fullPathInTemp + " " + ex.Message);
						}

					}

					ProcessJobUploads(job, ffc, request.UploadType, aacPerformedList, _currentUser); //(ffc.Count > 1 ? "multiart" : "singleart")
					foreach (var f in cleanList)
					{
						f.Close();
						f.Dispose();
					}

					ffc = null;

					foreach (var f in files)
					{
						var fullPathInTemp = Path.Combine(baseFolder, "TempUploads", f.Id.ToString());

						if (System.IO.File.Exists(fullPathInTemp))
						{
							System.IO.File.Delete(fullPathInTemp);
						}
					}
				}
				catch (Exception ex)
				{
					Log.Error(ex.Message);
				}

			#endregion process job attachments


			var result = _mapper.Map<OrderSummaryDtoWithJobs>(order);
			Response.Headers.Add("OrderId", order.Id.ToString());
			Response.Headers.Add("JobId", job.Id.ToString());
			return new OkObjectResult(result);
		}

		[Authorize(Roles = LepRoles.Staff)]
		[HttpPost("JobToDpc/{jobId:int}")]
		public IActionResult JobPushToDPC(int jobId, [FromServices] LabelPrinterApplication _labelPrinterApplication, [FromServices] PrintEngine PrintEngine)
		{
			var staff = (IStaff)_currentUser;
			var job = _jobApp.GetJob(jobId);

			job.AddComment(staff, "Pushing Job to DPC");
			job.Runs.ForEach(r => { r.Jobs.Remove(job); });
			job.Runs.Clear();

			job.PrintType = PrintType.D;
			job.SetStatus(JobStatusOptions.DPCPreProduction, staff);
			_jobApp.Save(job);

			if (job.Status == JobStatusOptions.DPCPreProduction)
				_labelPrinterApplication.Print(LabelType.DPCProcessing, job);
			PrintEngine.ReceivePrintQueue();

			return Ok();
		}


		[Authorize(Roles = LepRoles.Staff)]
		[HttpPost("Order/{orderId:int}/Job/{jobId:int}/ReprintRestart")]
		[Produces(typeof(OrderSummaryDtoWithJobs))]
		[ReturnBadRequestOnModelError]
		public IActionResult JobCommandReprintRestart([FromRoute] int orderId, [FromRoute] int jobId, [FromBody] ReprintRestartDto r)
		{
			try
			{
				IOrder o = null;
				var job = _jobApp.GetJob(jobId);

				if (r.Command == "reprint")
					o = _orderApp.ReprintJob(_currentUser, job, r.InvoicePrice, r.ReprintCost, r.CopyPreflight, true, r.Reason, r.Result, r.NcrNo, r.PredefinedReason, r.Quantity);
				else if (r.Command == "restart")
					o = _orderApp.RestartJob(job, r.InvoicePrice, r.ReprintCost, r.CopyPreflight, false, r.Reason, r.Result, r.NcrNo, r.PredefinedReason, r.Quantity);

				Response.Headers.Add("OrderId", o.Id.ToString());
				Response.Headers.Add("JobId", o.Jobs[0].Id.ToString());
				var message = $"{r.Command} of J{job.Id} has been issued as J{o.Jobs[0].Id}";

				return new ContentResult { StatusCode = (int)OK, Content = message };
			}
			catch (Exception ex)
			{
				return new ContentResult { StatusCode = (int)HttpStatusCode.BadRequest, Content = ex.Message };
			}
		}

		[HttpPost("Order/{orderId:int}/Job/{jobId:int}/NCopy/{n:int}")]
		[Produces(typeof(OrderSummaryDtoWithJobs))]
		[ReturnBadRequestOnModelError]
		public IActionResult JobCommandJobCopyNTimes([FromRoute] int orderId, [FromRoute] int jobId, int n)
		{
			var order = _orderApp.GetOrder(orderId);
			var job = _jobApp.GetJob(jobId);

			for (var i = 0; i < n; i++)
			{
				var copy = job.CopyJob();

				copy.Quantity = job.Quantity;
				copy.Price = job.Price;
				copy.PriceBase = job.PriceBase;
				copy.PriceDate = job.PriceDate;
				order.Jobs.Add(copy);
			}
			_orderApp.Save(order);
			return new ContentResult
			{
				StatusCode = (int)OK,
				Content = $"{n} copies of the the job has been created"
			};
		}
		[AllowAnonymous]
		[HttpGet("Order/{orderId:int}/jobs/{command}")]
		[HttpPost("Order/{orderId:int}/jobs/{command}")]
		[Produces(typeof(ContentResult))]
		public IActionResult JobsCommand([FromRoute] int orderId, [FromRoute] string command, [FromQuery] string jobIds)
		{
			var JobIds = jobIds.Split(',').Select(int.Parse).ToList();


			if (command == "RejectionOfVariationRequest")
			{
				var order = _orderApp.GetOrder(orderId);

				foreach (var jobId in JobIds)
				{
					var job = _jobApp.GetJob(jobId);
					job.AddComment(_currentUser, $"Rejection Of Variation Request");
					job.Status = JobStatusOptions.RejectedVariation;
					job.QuoteOutcome = QuoteStatus.Rejected.ToString();
				}
				order.Status = OrderStatusOptions.Open;
				_orderApp.Save(order);

				_emailApp.SendRejectionOfVariations(order);
				//_emailApp.SendNotification(job, ContentType.RejectionOfVariationRequest);
			}


			return Ok();
		}


		[HttpPost("Order/{orderId:int}/Job/{jobId:int}/{command}")]
		[Produces(typeof(ContentResult))]
		public IActionResult JobCommand([FromRoute] int orderId,
			[FromRoute] int jobId, [FromRoute] string command, [FromBody] string comment)
		{
			Log.Information($"{_currentUser.Username}  issuing {command} on job {jobId}");
			try
			{
				var order = _orderApp.GetOrder(orderId);
				var job = _jobApp.GetJob(jobId);
				if (!AccessAllowed(order))
				{
					return Forbid();
				}
				var message = "";


				if (command == "UnableToMeetPrice")
				{
					job.AddComment(_currentUser, $"Unable to meet price {job.CustomerRequestedPrice}");
					job.Status = JobStatusOptions.UnableToMeetPrice;
					order.Status = OrderStatusOptions.Open;
					_orderApp.Save(order);
					_emailApp.SendNotification(job, ContentType.UnableToMeetPrice);

				}


				if (command == "RejectionOfVariationRequest")
				{
					job.AddComment(_currentUser, $"Rejection Of Variation Request");
					job.Status = JobStatusOptions.RejectedVariation;
					order.Status = OrderStatusOptions.Open;
					_orderApp.Save(order);
					_emailApp.SendNotification(job, ContentType.RejectionOfVariationRequest);
				}


				// following 3 staff job-details.aspx.cs
				if (command == "Delete")
				{
					order.DeleteJob(job);
					_orderApp.Save(order);
					message = "Deleted job";
				}

				if (command == "RemoveArtworks")
				{
					if (!String.IsNullOrEmpty(comment))
					{
						var position = comment;
						var art = job.GetArtwork(comment);
						_jobApp.RemoveArtwork(job, art);

					}
					else
					{
						foreach (var art in job.Artworks)
						{
							_jobApp.RemoveArtwork(job, art);
						}
						message = "Deleted job artworks";
					}
					_jobApp.Save(job);
					//todo security, own orders artwork
				}

				if (command == "Reject")
				{

					try
					{
						var arts = job.Artworks.ToList();
						// make sure all files are closed first
						foreach (var art in arts)
						{
							if (!string.IsNullOrEmpty(art.Supplied))
							{
								var filename = LepGlobal.Instance.ArtworkPath(order, job, true, art.Supplied);
								if (LepGlobal.Instance.IsFileInUse(filename))
								{
									return new ContentResult { StatusCode = (int)HttpStatusCode.BadRequest, Content = $"Please close the file {filename} first" };
								}
							}

							if (!string.IsNullOrEmpty(art.Ready))
							{
								var filename = LepGlobal.Instance.ArtworkPath(order, job, false, art.Ready);
								if (LepGlobal.Instance.IsFileInUse(filename))
								{
									return new ContentResult { StatusCode = (int)HttpStatusCode.BadRequest, Content = $"Please close the file {filename} first" };
								}
							}
						}

						// make sure all files are closed first
						var jobRootFolderStr = LepGlobal.Instance.ArtworkDirectory(job, false);

						var jobRootFolderRejectedFilesFolder = new DirectoryInfo(Path.Combine(jobRootFolderStr.FullName, "rejected-files"));
						if (!jobRootFolderRejectedFilesFolder.Exists)
						{
							jobRootFolderRejectedFilesFolder.Create();
						}
						var nowStr = System.DateTime.Now.ToString("yyyy-MM-dd HHmm");
						var datedBackupFolder = jobRootFolderRejectedFilesFolder.CreateSubdirectory(nowStr);

						foreach (var art in arts)
						{
							if (!string.IsNullOrEmpty(art.Supplied))
							{
								var filename = LepGlobal.Instance.ArtworkPath(order, job, true, art.Supplied);
								try { datedBackupFolder.CreateSubdirectory("from-customer"); } catch (Exception ex) { }
								var backupAt = Path.Combine(datedBackupFolder.FullName, "from-customer", System.IO.Path.GetFileName(filename));
								System.IO.File.Copy(filename, backupAt);
							}

							if (!string.IsNullOrEmpty(art.Ready))
							{
								var filename = LepGlobal.Instance.ArtworkPath(order, job, false, art.Ready);
								var backupAt = Path.Combine(datedBackupFolder.FullName, System.IO.Path.GetFileName(filename));
								System.IO.File.Copy(filename, backupAt);
							}
						}


						var rejectedReasonTxt = "\n\n" + _currentUser.FirstName + " " + _currentUser.LastName + " @" + nowStr;
						System.IO.File.WriteAllText(Path.Combine(datedBackupFolder.FullName, "rejected reason.txt"), comment);

					}
					catch (Exception ex)
					{
						Log.Error("Reject Workflow: " + ex.Message);
					}

					job.Reject((IStaff)_currentUser, comment);
					order.DispatchEst = null;
					_orderApp.Save(order);
					message = "Job Rejected";
				}

				//if (command == "Withdraw") {
				//	job.Withdraw(_currentUser);
				//	_orderApp.Save(order);
				//	message = "Job Withdrawn";
				//}

				//if (command == "Reactivate") {
				//	job.Reactivate(_currentUser);
				//	_orderApp.Save(order);
				//	message = "Job Reactivated";
				//}

				if (command == "moveArtwork")
				{
					_jobApp.CopyArtwork(job);
					message = "Job artwork moved";
				}

				if (command == "ApproveQuote")
				{
					// requires save job
					job.ApproveQuote(_currentUser);
					_orderApp.Save(order);
					message = "You approval of the quote has been saved";
				}

				if (command == "ApproveQuoteAndSubmit")
				{
					// requires save job
					job.ApproveQuote(_currentUser);
					_orderApp.Save(order);
					return OrderSubmit(order.Id);
				}

				if (command == "AcceptSupplyArtwork")
				{
					// requires save job
					job.AcceptSupplyArtwork(_currentUser);
					_orderApp.Save(order);
					message = "Supply artwork accepted";
				}

				if (command == "AcceptSupplyArtworkWithSubmit")
				{
					// requires save job
					job.AcceptSupplyArtwork(_currentUser);
					_orderApp.Save(order);
					return OrderSubmit(order.Id);
				}

				if (command == "Copy")
				{
					//var copy = job.CopyJob();
					//var modQty = copy.Quantity;

					////:TODO MikeGreenan Customer ProductPriceCode stuff
					////if current user is Customer then use customer.ProductPriceCode
					////else if Staff use job.Order.Customer.ProductPriceCode
					//var productpriceCode = "";
					//if (_currentUser is ICustomerUser) productpriceCode = ((ICustomerUser)_currentUser).ProductPriceCode;
					//else productpriceCode = copy.Order.Customer.ProductPriceCode;

					////todo: MikeGreenan - The following may not be needed - investigate.

					//var log = new StringBuilder();

					//var price = _pricingEngine.PriceJob(copy, log, out modQty, productpriceCode);
					//if (price != 0) copy.Quantity = modQty;
					//copy.SetPrice(_currentUser, price, 0, true);
					var newOrder = _orderApp.CopyOrder2(order, job);
					var newJob = newOrder.Jobs[0];
					message = $"{newJob.Id} created from {job.Id}";

					Response.Headers.Add("OrderId", newOrder.Id.ToString());
					Response.Headers.Add("JobId", newJob.Id.ToString());
				}

				if (command == "Reorder")
				{
					var o = _orderApp.ReorderJob(job);
					//todo: redirect to o
					Response.Headers.Add("OrderId", o.Id.ToString());
					Response.Headers.Add("JobId", o.Jobs[0].Id.ToString());
					message = $"J{job.Id} has been reordered as J{o.Jobs[0].Id}";
				}

				if (command == "")
				{
				}

				return new ContentResult { StatusCode = (int)OK, Content = message };
			}
			catch (Exception ex)
			{
				return new ContentResult { StatusCode = (int)HttpStatusCode.BadRequest, Content = ex.ToString() };
			}
		}

		#region Job artwork related functions

		//// todo check if this is used, if not delete
		//[HttpPost("job/{jobId:int}/{position}")]
		//public IActionResult UploadArtwork (int jobId, string position)
		//{
		//    var job = _jobApp.GetJob(jobId);

		//    job.GetArtwork("multiart");

		//    long size = 0;
		//    var files = Request.Form.Files;
		//    foreach (var file in files) {
		//        var filename = ContentDispositionHeaderValue.Parse(file.ContentDisposition).FileName.Trim('"');
		//        filename = env.ContentRootPath + $@"\~\data\uploads\{filename}";
		//        size += file.Length;
		//        using (var fs = System.IO.File.Create(filename)) {
		//            file.CopyTo(fs);
		//            fs.Flush();
		//        }
		//    }

		//    var str = $"{files.Count} file(s) / {size} bytes uploaded successfully!";
		//    return Ok(str);
		//}

		[HttpGet("Job/{jobId:int}/Artworks")]
		[Produces(typeof(List<ArtworkDto>))]
		public IActionResult GetJobArtworks(int jobId)
		{
			var job = _jobApp.GetJob(jobId);
			var result = job.Artworks.Select(_mapper.Map<ArtworkDto>).ToList();
			return new OkObjectResult(result);
		}

		[HttpGet("order/{orderId:int}/Thumbs")]
		[Produces(typeof(List<ArtworkDto>))]

		public IActionResult GetOrderThumbs(int orderId)
		{
			var order = _orderApp.GetOrder(orderId);
			if (order == null) return NotFound();

			if (!AccessAllowed(order))
			{
				return Forbid();
			}

			dynamic result = new ExpandoObject();
			result = order.Jobs.ToDictionary(
				j => j.Id,
				j => new
				{
					Thumbs = _cachedAccessTo.Thumbnails(j).Take(2)
							.Select(f => $"api/orders/Job/{j.Id}/thumb/{j.DateModified.Ticks}/{f.Name}").ToList(),
				}
				);
			return new OkObjectResult(result);
		}

		[HttpGet("Job/{jobId:int}/{artworkId:int}/{issupply:bool}/download")]
		[HttpPost("Job/{jobId:int}/{artworkId:int}/{issupply:bool}/download")]
		[Produces(typeof(FileContentResult))]
		[ResponseCache(Duration = 60 * 60 * 24, VaryByQueryKeys = new[] { "*" }, Location = ResponseCacheLocation.Client)]
		[AllowAnonymous]
		public IActionResult DownloadArtworks(int jobId, int artworkId, bool issupply)
		{
			var job = _jobApp.GetJob(jobId);
			if (job == null) return NotFound();

			var order = job.Order;
			if (!AccessAllowed(order))
			{
				return Forbid();
			}

			var art = job.Artworks.SingleOrDefault(a => a.Id == artworkId);
			if (art == null)
				return new ContentResult
				{
					StatusCode = StatusCodes.Status400BadRequest,
					Content = $"No artwork with that Id exists for the Job"
				};

			var filename = LepGlobal.Instance.ArtworkPath(order, job, issupply, art.Supplied);
			var f = new FileInfo(filename);

			if (!f.Exists)
			{
				filename = LepGlobal.Instance.ArtworkPath(order, job, !issupply, !issupply ? art.Ready : art.Supplied);
				f = new FileInfo(filename);
				if (!f.Exists)
					return new ContentResult { StatusCode = StatusCodes.Status400BadRequest, Content = $"File does not exist" };
			}

			var fileStream = new FileStream(f.FullName, FileMode.Open, FileAccess.Read);
			//TOTO return File(fileStream, MimeMapping.GetMimeMapping(f.FullName), art.Supplied);
			return File(fileStream, "application/pdf", art.Supplied);
		}

		public static Dictionary<JobTypeOptions, string> timg = new Dictionary<JobTypeOptions, string>(){
			{ BusinessCard          ,  "Business-Cards.svg"},
			{ BusinessCardNdd       ,  "Business-Cards.svg"},
			{ BusinessCardSdd       ,  "Business-Cards.svg"},
			{Brochure               ,  "brochure-folded.svg"},
			{BrochureNDD            ,  "brochure-folded.svg"},
			{BrochureSDD            ,  "brochure-folded.svg"},
			{BrochureSpecial        ,  "brochure-folded.svg"},
			{Magazine               ,  "Magazines-Booklets.svg"},
			{MagazineNDD            ,  "Magazines-Booklets.svg"},
			{MagazineSeparate       ,  "Magazines-Booklets.svg"},
			{Stationery             ,  "Stationery.svg"},
			{StationerySDD          ,  "Stationery.svg"},
			{PresentationFolder     ,  "Presentation-Folders.svg"},
			{PresentationFolderNDD  ,  "Presentation-Folders.svg"},
			{GolfScoreCards         ,  "Golf-Score-Cards.svg"},
			{Notepads               ,  "Notepads-Deskpads.svg"},
			{Postcard               ,  "Postcards.svg"},
			{Poster                 ,  "Posters.svg"},
		};

		[AllowAnonymous]
		[HttpGet("Job/{jobId:int}/thumb/{tname}")]
		[HttpPost("Job/{jobId:int}/thumb/{tname}")]
		[Produces(typeof(FileContentResult))]
		[ResponseCache(Duration = 60 * 60 * 24, VaryByQueryKeys = new[] { "*" }, Location = ResponseCacheLocation.Client)] //
		public IActionResult GetThumbnail(int jobId, string tname)
		{
			try
			{
				var job = _jobApp.GetJob(jobId);
				if (job == null) return NotFound();
				var order = job.Order;

				var artworkPath = LepGlobal.Instance.ArtworkPath(order, job, false, "");
				var dir = new DirectoryInfo(artworkPath);
				var fn = dir.GetFiles(tname, SearchOption.AllDirectories).FirstOrDefault();
				if (fn != null)
				{
					var stream = new FileStream(fn.FullName, FileMode.Open, FileAccess.Read, FileShare.Read);
					return new FileStreamResult(stream, "image/png");
				}

				var url = "";
				timg.TryGetValue((JobTypeOptions)job.Template.Id, out url);

				if (url != "")
				{

					string piimg = Path.Combine(_config["StaticAssets"], "images", "ProductIcon2", url);
					try
					{
						var stream = new FileStream(piimg, FileMode.Open, FileAccess.Read, FileShare.Read);
						return new FileStreamResult(stream, "image/svg+xml");
					}
					catch (Exception ex)
					{
						var m = ex.Message;
					}
				}
				return BadRequest();
			}
			catch (Exception)
			{
				return BadRequest();
			}
		}

		//[HttpGet("Job/{jobId:int}/thumb/{dateModified}/{tname}")]
		//[HttpPost("Job/{jobId:int}/thumb/{dateModified}/{tname}")]
		//[Produces(typeof(FileContentResult))]
		//[ResponseCache(Duration = 864000, VaryByHeader = "*", Location = ResponseCacheLocation.Client)]
		//public async Task<IActionResult> GetThumbnail(int jobId, string dateModified, string tname, [FromServices] IHostingEnvironment env)
		//{
		//	var job = _jobApp.GetJob(jobId);
		//	if (job == null) return NotFound();

		//	var order = job.Order;
		//	if (_currentUserIsCust && order.Customer.Id == _currentUser.Id) {
		//		var art = job.Artworks.FirstOrDefault(a => a.Position == "front"
		//												|| a.Position == "multiart"
		//												|| a.Position == "Cover"
		//												|| a.Position == "Text"
		//												);
		//		if (art != null) {
		//			bool fromCustomer = true;
		//			string artfile = art.Supplied;
		//			if (!string.IsNullOrEmpty(art.Ready)) {
		//				artfile = art.Ready;
		//				fromCustomer = false;
		//			}

		//			string pattern = tname;

		//			var artworkPath = _configApp.ArtworkPath(order, job, fromCustomer, "");
		//			var dir = new DirectoryInfo(artworkPath);
		//			if (dir.Exists)
		//			{
		//				var thumbs = dir.GetFiles(pattern);
		//				var fn = thumbs.FirstOrDefault(t => t.Name == tname);

		//				if (fn!=null ) {
		//					var stream = new FileStream(fn.FullName, FileMode.Open, FileAccess.Read, FileShare.Read);
		//					return new FileStreamResult(stream, "image/png");
		//				}
		//			}
		//		}

		//	}
		//	var url = "";
		//	timg.TryGetValue((JobTypeOptions)job.Template.Id, out url);

		//	if (url != "") {
		//		//var url2 = Path.Combine(env.WebRootPath, @"images\ProductIcon2" ,url);
		//		var url2 = env.WebRootPath + @"\images\ProductIcon2\" + url;
		//		try {
		//			var stream = new FileStream(url2, FileMode.Open, FileAccess.Read, FileShare.Read);
		//			return new FileStreamResult(stream, "image/svg+xml");

		//		}
		//		catch (Exception ex) {
		//			var m = ex.Message;
		//		}

		//[HttpPost("job/{jobId:int}/artworks")]
		//[DisableRequestSizeLimit]
		//public IActionResult SaveJobArtworks(int jobId)
		//{
		//	long size = 0;
		//	var files = Request.Form.Files;
		//	dynamic uploadReq = JsonConvert.DeserializeObject(Request.Form["vm"]);
		//	var jobId2 = uploadReq.jobId;
		//	string selectedUploadType = uploadReq.UploadType;
		//	var job = _jobApp.GetJob(jobId);


		//	ProcessJobUploads(job, files, selectedUploadType, _currentUser);
		//	var str = $"{files.Count} file(s) / {size} bytes uploaded successfully!";
		//	return GetJobArtworks(jobId);
		//}


		[HttpPost("job/cartonlabel2")]
		public IActionResult PrintCartonLabel2([FromBody] dynamic r)
		{
			var job = _jobApp.GetJob((int)r.jobId);
			if (job == null) return NotFound();
			var copies = 1;
			var orderId = job.Order.Id;

			var label = "Manual Carton Label";




			var printerName = (string)r.printerName;
			var printName = $"O{job.Order.Id}-J{job.Id}-{label}";
			var ipadd = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString();


			var report = new FastReport.Report();

			var jobQty = (int)r.jobQty;
			var jobNo = job.Id;
			var jobName = (string)r.jobName;
			var orderNo = job.Order.Id;

			var mhText = job.BrochureDistPackInfo?.ToInstructionsText2() ?? "";

			var remainder = jobQty % (int)r.qtyPerCarton;
			var totalCarton = (int)Math.Ceiling(jobQty / (float)r.qtyPerCarton);


			var dsx = from x in System.Linq.Enumerable.Range(1, totalCarton)
					  select new
					  {
						  quantity = ((remainder > 0 && x == totalCarton) ? remainder : (int)r.qtyPerCarton),
						  r = $"{x} of {totalCarton}"
					  };

			var dsxstr = JsonConvert.SerializeObject(dsx, Newtonsoft.Json.Formatting.Indented);
			var cs = "Json='" + dsxstr + "'";


			var reportPath = _config["Labels:CartonLabelMH"];
			report.Load(reportPath);

			report.SetParameterValue("varJobName", jobName.Sanitize());
			report.SetParameterValue("varOrderNo", orderNo);
			report.SetParameterValue("varJobNo", jobNo);
			report.SetParameterValue("varMailHouse", mhText);

			report.Dictionary.Connections[0].ConnectionString = cs;

			PrintUtils.PrintReport(report, printerName, copies, printName);
			return Ok();
		}


		[HttpPost("job/cartonlabel")]
		public IActionResult PrintCartonLabel([FromBody] dynamic r)
		{
			var job = _jobApp.GetJob((int)r.jobId);
			if (job == null) return NotFound();
			var copies = 1;
			var orderId = job.Order.Id;

			var label = "Manual Carton Label";

			var printerName = _configApp.GetValue(job.Facility == Facility.FG ?
				Configuration.FG_DispatchCartonLabelPrinterName : Configuration.PM_DispatchCartonLabelPrinterName);

			var ipadd = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString();

			if (ipadd.Is("**********")) // FG-DPC-03
			{
				printerName = "FG.DPC_CartonLabel";
			}
			else if (ipadd.Is("**********", "***********"))  // Stitcher PCs in FG
			{
				printerName = "FG.Stitcher_CartonLabel";
			}

			var report = new FastReport.Report();




			var jobQty = (int)r.jobQty;
			var jobNo = job.Id;
			var jobName = (string)r.jobName;
			var orderNo = job.Order.Id;


			var remainder = jobQty % (int)r.qtyPerCarton;
			var totalCarton = (int)Math.Ceiling(jobQty / (float)r.qtyPerCarton);


			var dsx = from x in System.Linq.Enumerable.Range(1, totalCarton)
					  select new
					  {
						  quantity = ((remainder > 0 && x == totalCarton) ? remainder : (int)r.qtyPerCarton),
						  r = $"{x} of {totalCarton}"
					  };

			var dsxstr = JsonConvert.SerializeObject(dsx, Newtonsoft.Json.Formatting.Indented);
			var cs = "Json='" + dsxstr + "'";


			var reportPath = _config["Labels:CartonLabel"];
			report.Load(reportPath);

			report.SetParameterValue("varJobName", jobName.Sanitize());
			report.SetParameterValue("varOrderNo", orderNo);
			report.SetParameterValue("varJobNo", jobNo);

			report.Dictionary.Connections[0].ConnectionString = cs;

			var printName = $"O{job.Order.Id}-J{job.Id}-{label}";
			PrintUtils.PrintReport(report, printerName, copies, printName);

			return Ok();
		}


		[HttpGet("job/{jobId:int}/cartonlabel")]
		public IActionResult GetPrintCartonLabel(int jobId)
		{
			var job = _jobApp.GetJob(jobId);
			if (job == null)
				return new OkObjectResult(null);
			// get all package from order
			var packages = job.Order.PackDetail.GetPackages(null).Where(_ => _.Level >= 2 && _.Level < 9 && _.JobId == jobId);
			var mailHouse = job.BrochureDistPackInfo?.ToInstructionsText() ?? "";
			return new OkObjectResult(new
			{
				JobId = jobId,
				JobQuantity = job.Quantity,
				JobName = job.Name,
				OrderId = job.Order.Id,
				Packages = packages,
				CountOfPackages = packages.Count(),
				MailHouse = mailHouse
			});
		}



		[HttpGet("job/{jobId:int}/logolabel")]
		public IActionResult GetPrintLogoLabel(int jobId)
		{
			var job = _jobApp.GetJob(jobId);
			if (job == null)
				return new OkObjectResult(null);
			// get all package from order
			var packages = job.Order.PackDetail.GetPackages(null).Where(_ => _.Level >= 2 && _.Level < 9 && _.JobId == jobId);
			return new OkObjectResult(new
			{
				JobId = jobId,
				JobQuantity = job.Quantity,
				JobName = job.Name,
				OrderId = job.Order.Id,
				Facility = job.Facility.ToString(),
				Packages = packages,
				CountOfPackages = packages.Count(),
				CustomerName = job.Order.Customer.Name,
				LogoRef = job.Order.CustomerLogoYourRef,
				CustomerId = job.Order.Customer.Id,

				Logo = _userApplication.GetCustomerLogo(job.Order.Customer.Username)?.FullName ?? null
			});
		}


		/// <summary>
		/// Save or Update an Order Object
		/// </summary>
		/// <param name="orderId"></param>
		/// <param name="dto"></param>
		[HttpPost("Order/{orderId:int}")]
		[ReturnBadRequestOnModelError]
		public IActionResult SaveOrder(int orderId, [FromBody][Required] OrderUpdateDto dto)
		{
			Log.Information("{User} saving Order {OrderId}", _currentUser.Username, orderId);
			var order = _orderApp.GetOrder(orderId);

			//var oldPrice = order.PackDetail.Price;
			//var updToPrice = dto.PackDetail.Price;

			if (!dto.DeliveryAddress.Postcode.Equals(order.DeliveryAddress.Postcode))
			{
				// address changed
				dto.Courier = CourierType.None;
			}

			order = _mapper.Map(dto, order);

			#region change job names

			if (dto.ChangedNames != null)
				foreach (var e in dto.ChangedNames)
				{
					try
					{
						var job = order.Jobs.FirstOrDefault(j => j.Id == e.Key);
						job.Name = e.Value;
					}
					catch { }
				}

			#endregion change job names

			if (dto.CommentsToAdd != null)
				foreach (var e in dto.CommentsToAdd)
				{
					try
					{
						var job = order.Jobs.FirstOrDefault(j => j.Id == e.Key);
						var comments = e.Value; // must assign to a var other wise e.value in the foreach does not work
						foreach (var comment in comments)
						{
							job.AddComment(_currentUser, comment.CommentText, false);
						}
					}
					catch { }
				}

			#region process job attachments

			var baseFolder = _config["DataDirectory"];
			if (dto.AttachmentsToProcess != null)
			{
				foreach (var e in dto.AttachmentsToProcess)
				{
					try
					{
						var job = order.Jobs.FirstOrDefault(j => j.Id == e.Key);
						var files = e.Value;
						var aacPerformedList = e.Value.Select(_ => _.AACPerformed).ToList();
						var cleanList = new List<FileStream>();
						var ffc = new FormFileCollection();

						foreach (var f in files)
						{
							var fullPathInTemp = Path.Combine(baseFolder, "TempUploads", f.Id.ToString());

							try
							{
								// Ensure destination directory exists
								var folderName = LepGlobal.Instance.ArtworkPath(order, job, true, "");
								if (!Directory.Exists(folderName))
								{
									Directory.CreateDirectory(folderName);
								}

								// Define source and destination paths
								var sourceFilePath = Path.Combine(baseFolder, "TempUploads", f.Name.ToString());
								var destinationFilePath = LepGlobal.Instance.ArtworkPath(order, job, true, f.Name);

								// Delete any existing file at the destination
								if (System.IO.File.Exists(destinationFilePath))
								{
									System.IO.File.Delete(destinationFilePath);
								}

								// Copy the file from source to destination
								System.IO.File.Copy(sourceFilePath, destinationFilePath);

								// Prepare the file stream and form file for processing
								var ifs = new FileStream(fullPathInTemp, FileMode.Open);
								cleanList.Add(ifs); // Add to list for later cleanup
								var ff = new FormFile(ifs, 0, ifs.Length, f.Position, f.Name);
								ffc.Add(ff);
							}
							catch (Exception ex)
							{
								Log.Error("Cannot process file " + fullPathInTemp + ": " + ex.Message);
							}
						}

						// Determine upload type and process uploads
						var uploadType = files.Any(f => f.Name == "multiart") ? "multiart" : "singleart";
						ProcessJobUploads(job, ffc, uploadType, aacPerformedList, _currentUser);

						// Clean up the streams
						foreach (var f in cleanList)
						{
							f.Close();
							f.Dispose();
						}

						ffc = null;

						// Delete temporary files
						foreach (var f in files)
						{
							var fullPathInTemp = Path.Combine(baseFolder, "TempUploads", f.Id.ToString());

							if (System.IO.File.Exists(fullPathInTemp))
							{
								System.IO.File.Delete(fullPathInTemp);
							}
						}
					}
					catch (Exception ex)
					{
						Log.Error(ex.Message);
					}
				}
			}

			#endregion process job attachments


			if (!(_currentUserIsAnonymousWLCustomer || _currentUserIsLoggedInWLCustomer))
			{
				_orderApp.ScatteredLogic_PreSaveOrder_ModifyPackDetailAndApplyFCode(order, _currentUser);
				_orderApp.ScatteredLogic_PreSaveOrder_SendEmail(order, _currentUser);
			}

			if (dto.WLCustomerId != null)
			{
				order.WLCustomerId = dto.WLCustomerId;
			}

			_orderApp.Save(order);
			return Ok();
		}

		[HttpPost("Order/{orderId:int}/associate")]
		[Authorize(LepRoles.LoggedInWLCustomer)]
		public IActionResult Associate(int orderId)
		{
			var order = _orderApp.GetOrder(orderId);
			var subCustId = User.Claims.Where(c => c.Type == "SubCustId").Select(c => Convert.ToInt32(c.Value)).FirstOrDefault();

			if (order.WLCustomerId == null)
			{
				order.WLCustomerId = subCustId;
			}
			_orderApp.Save(order);
			return Ok();
		}

		protected void ProcessJobUploads(IJob job, IFormFileCollection files, string selectedUploadType, List<bool?> aacPerformed, IUser CurrentUser)
		{
			_jobApp.ArchieveArtwork(job);
			var positions = job.GetRequiredPosition();

			switch (selectedUploadType)
			{
				case "multiart":
				case "singleart":
					//foreach (var file in files)
					for (int i = 0; i < files.Count; i++)
					{
						//var filename = ContentDispositionHeaderValue.Parse(file.ContentDisposition).FileName.Trim('"');
						var file = files[i];
						var filename = file.FileName;
						var position = file.Name;
						var art = job.GetArtwork(position);
						if (art != null)
						{
							_jobApp.RemoveArtwork(job, art);
						}


						art = job.AddArtwork(position);
						art.AACPerformed = aacPerformed[i];
						_jobApp.SetSupplyArt(job, art, filename);
						using (var stream = file.OpenReadStream())
						{
							_jobApp.SaveArtwork(art, stream);
						}

						try
						{
							// copy from tempUploads to  dest artwork folder
							// all matching likes of
							//    thumbnail_input.1.png
							// to thumbnail_input_multi.1.png
							var srcFolder = Path.Combine(_config["DataDirectory"], "TempUploads");
							var destFolderName = LepGlobal.Instance.ArtworkPath(job.Order, job, true, "");



							var baseName = Path.GetFileNameWithoutExtension(filename);
							// get files mathing  $"thumb_{art.Supplied}*.png" in tempUploads folder copy them to artwork folder
							var filesToCopy = Directory.GetFiles(srcFolder, $"thumbnail_{baseName}.*.png");
							foreach (var f in filesToCopy)
							{
								var match = System.Text.RegularExpressions.Regex.Match(f, @"\.(\d+)\.png");
								if (match.Success)
								{
									var extractedPart = match.Value; // This will be ".1."
									var dname = "thumbnail_" + Path.GetFileNameWithoutExtension(art.Supplied) + extractedPart;
									var dest = Path.Combine(destFolderName, dname);
									System.IO.File.Copy(f, dest, true);
								}
							}
						}
						catch (Exception ex)
						{

						}


					}
					job.ArtworkStatus = ArtworkStatusOption.None;
					if (job.IsArtworkValidForSubmit())
					{
						job.SupplyArtworkApproval = JobApprovalOptions.NotNeeded;
					}

					break;

				case "Later":
					job.ArtworkStatus = ArtworkStatusOption.LATER;
					break;
			}



			bool isUploadingMultiPageArt = files.Any(f => f.Name == "multiart");
			if (isUploadingMultiPageArt)
			{
				job.Artworks.Where(_ => _.Position != "multiart" && positions.Contains(_.Position))
					.ToList().ForEach(_ => _jobApp.RemoveArtwork(job, _));

			}
			else
			{
				job.Artworks.Where(_ => _.Position == "multiart" || !positions.Contains(_.Position))
					.ToList().ForEach(_ => _jobApp.RemoveArtwork(job, _));
			}

			_jobApp.Save(job);


			_jobApp.MergeFrontAndBackArtwork(job);
		}



		#endregion Job artwork related functions

		//[HttpPost("Order/{orderId:int}/Job/{jobId:int}/NameImg")]
		//public IActionResult SaveJobSmallDetails([FromRoute] int orderId, [FromRoute] int jobId)
		//{
		//	if (orderId == 0 || jobId == 0) return BadRequest();

		//	Log.Information($"{_currentUser.Username} SaveJobSmallDetails {orderId} - {jobId} ");
		//	//var s = JsonConvert.SerializeObject(request);
		//	var request = JsonConvert.DeserializeObject<JobViewCustDto>(Request.Form["request"]);

		//	// IPhysicalAddress address = null;
		//	IOrder order = null;
		//	IJob job = null;

		//	ICustomerUser customer = null;

		//	if (_currentUser is ICustomerUser)
		//	{
		//		var custId = User.Claims.Where(c => c.Type == "UserId").Select(c => c.Value).FirstOrDefault();
		//		customer = (ICustomerUser)_userApplication.GetUser(Convert.ToInt32(custId));
		//	}

		//	order = _orderApp.GetOrder(orderId);
		//	job = _jobApp.GetJob(jobId);

		//	job.Name = request.Name;

		//	if (Request.Form.Files.Count > 0) ProcessJobUploads(job, Request.Form.Files, request.UploadType, _currentUser);

		//	_orderApp.Save(order);

		//	var result = _mapper.Map<OrderSummaryDtoWithJobs>(order);
		//	return new OkObjectResult(result);
		//}

		public class CreateConsignmentRequest
		{
			public int OrderId { get; set; }
			public Facility Facility { get; set; }
			public string ConsignmentPrinterName { get; set; }
			//public string CarrierName { get; set; }
			//public string ServiceName { get; set; }
			//public string AccountNumber { get; set; }
		}

		[HttpPost("Order/CreateConsignment")]
		public IActionResult CreateConsignment([FromServices] ICourierApplication courierApp, [FromBody] CreateConsignmentRequest ccr)
		{
			Log.Information("*****");
			Log.Information("ccr {@ccr}", ccr);

			if (ccr.OrderId == 0 || !Enum.IsDefined(typeof(Facility), ccr.Facility)
				//|| string.IsNullOrEmpty(ccr.CarrierName) || string.IsNullOrEmpty(ccr.ServiceName) || string.IsNullOrEmpty(ccr.AccountNumber)
				)
			{
				return BadRequest();
			}
			var order = _orderApp.GetOrder(ccr.OrderId);

			Facility facility = ccr.Facility;

			//Log.Information("ccr {@ccr}", ccr);

			var result = courierApp.CreateConsignment(order, ccr.Facility, ccr.ConsignmentPrinterName);
			return new OkObjectResult(result);
		}

		public class DispatchPrintRequest
		{
			public string label { get; set; }
			public string printerName { get; set; }
			public int orderId { get; set; }
			public int jobId { get; set; }
			public int copies { get; set; }
			public string courier { get; set; }

			// split index
			public int? sIdx { get; set; }
		}


		[HttpPost("Order/DispatcherPrint")]
		public IActionResult DispatcherPrint([FromBody] DispatchPrintRequest pr)
		{

			if (pr == null) return BadRequest();
			var printName = $"O{pr.orderId}-J{pr.jobId}-{pr.label}";
			try
			{
				var label = pr.label;
				var printerName = pr.printerName;
				var orderId = pr.orderId;
				var jobId = pr.jobId;
				var copies = pr.copies;

				Log.Information("DispatchPrint {jobId} {label} {printerName} {copies}", jobId, label, printerName, copies);

				var order = _orderApp.GetOrder(orderId);
				if (order == null) return NotFound();
				if (copies == 0) copies = 1;

				var job = order.Jobs.Where(j => j.Id == jobId).FirstOrDefault();
				if (job == null) return NotFound();



				var varName = order.ReceiverName;
				var varContactPhoneNo = $"Contact Phone: {order.ReceiverPhone}";
				var varAddress = order.DeliveryAddress.ToStringFormatted();
				var varReferenceNo = $"Reference No: {orderId}";
				var varCustLogoYourRef = order.CustomerLogoYourRef ?? "";
				if (pr.sIdx != null && job.Splits.Count > pr.sIdx)
				{
					var split = job.Splits[pr.sIdx.Value];
					varName = split.RecipientName;
					varContactPhoneNo = $"Contact Phone: {split.RecipientPhone}";
					varAddress = split.Address.ToStringFormatted();
					varCustLogoYourRef = split.CustomerLogoYourRef ?? varCustLogoYourRef;
				}

				Report report = new Report();

				Action<string, string> _ = (k, v) => (report.FindObject(k) as TextObject).Text = v;
				// logo
				if (label == "LogoLabel")
				{
					var logoFile = _userApplication.GetCustomerLogo(order.Customer.Username);
					if (logoFile == null)
					{
						Log.Error($"Cant find logo file for {order.Customer.Username}");
						return new BadRequestObjectResult(new { error = "can not find logo file" });
					}

					var name = _config["Labels:LogoLabel"];
					report.Load(name);

					// set image
					var pic = report.FindObject("picLogo") as PictureObject;
					pic.Image = new System.Drawing.Bitmap(logoFile.FullName);
					pic.ShouldDisposeImage = true;

					// set cust reference
					_("memReference", varCustLogoYourRef);
					PrintUtils.PrintReport(report, printerName, copies, printName);

				}
				else if (label == "PayMeLabel")
				{
					report.Load(_config["Labels:PayMeLabel"]);
					_("varOrderDetails", $"{orderId}\n{order.Customer.Name.Sanitize()}");
					_("varJobDetails", $"Job#{jobId}\nJob Name: {job.Name.Sanitize()}");
					PrintUtils.PrintReport(report, printerName, copies, printName);
				}
				else if (label == "FillingLabel")
				{
					report.Load(_config["Labels:FillingLabel"]);
					_("varOrderDetails", $"{orderId}\n{order.Customer.Name.Sanitize()}");
					_("varJobDetails", $"Job#{jobId}\nJob Name: {job.Name.Sanitize()}");
					PrintUtils.PrintReport(report, printerName, copies, printName);
				}
				else if (label == "AddressA4Label")
				{
					report.Load(_config["Labels:AddressA4Label"]);
					_("varName", varName);
					_("varContactPhoneNo", varContactPhoneNo);
					_("varAddress", varAddress);
					_("varReferenceNo", varReferenceNo);
					PrintUtils.PrintReport(report, printerName, copies, printName);
				}
				else if (label == "AddressLabel")
				{
					report.Load(_config["Labels:SampleLabel"]);
					_("varComment", "");
					_("varName", varName);
					_("varAddress", varAddress);
					_("varReferenceNo", varReferenceNo);
					_("varContactPhoneNo", varContactPhoneNo);
					PrintUtils.PrintReport(report, printerName, copies, printName);
				}
				else if (label == "AddressLabelOther")
				{
					report.Load(_config["Labels:SampleLabel"]);
					_("varComment", pr.courier.Split('~')[1]);
					_("varName", varName);
					_("varAddress", varAddress);
					_("varReferenceNo", varReferenceNo);
					_("varContactPhoneNo", varContactPhoneNo);
					PrintUtils.PrintReport(report, printerName, copies, printName);
				}
				else if (label == "SampleLabel")
				{
					report.Load(_config["Labels:SampleLabel"]);
					_("varComment", "");
					_("varName", order.Customer.Name.Sanitize());
					_("varAddress", order.Customer.BillingAddress.ToStringFormatted());
					_("varReferenceNo", $"");
					_("varContactPhoneNo", "Samples enclosed.");
					PrintUtils.PrintReport(report, printerName, copies, printName);
				}
				else if (label == "PickUpLabel")
				{
					var phone = order.Customer.Contact1.PhoneFull().NullIf() ?? order.Customer.Contact1.Mobile.NullIf() ?? "-";

					report.Load(_config["Labels:PickupLabel"]);
					_("varComment", $"{order.RecipientName.Sanitize()}");
					_("varName", order.Customer.Name);
					_("varReferenceNo", $"{orderId}");
					_("varContactPhoneNo", $"Contact Phone: {phone}");
					PrintUtils.PrintReport(report, printerName, copies, printName);
				}
				else if (label == "OneDeliveryOnly")
				{
					report.Load(_config["Labels:OneDeliveryOnly"]);
					PrintUtils.PrintReport(report, printerName, copies, printName);
				}
				report.Dispose();

			}
			catch (Exception ex)
			{
				Log.Error(ex, ex.Message);
				return new OkObjectResult(false);
			}

			return new OkObjectResult(true);
		}


		[HttpPost("Order/{orderId:int}/SyncConnotes")]
		public IActionResult SyncConnotes([FromRoute] int orderId)
		{
			var order = _orderApp.GetOrder(orderId);
			order.ConNotes.Clear();
			_courierApplication.SyncConnotes(order, Facility.FG);
			_courierApplication.SyncConnotes(order, Facility.PM);

			return Ok();
		}



		[HttpPost("Order/{orderId:int}/sendPickupReadyEmail")]
		public IActionResult SendPickupReadyEmail([FromRoute] int orderId)
		{
			var order = _orderApp.GetOrder(orderId);
			_emailApp.SendNotification(order, ContentType.OrderReadyForPickup);
			return Ok();
		}


		[HttpGet("job/{jobId:int}/SingleJobsThickness")]
		public decimal GetSingleJobsThickness(int jobId)
		{
			var j = _jobApp.GetJob(jobId);
			var jd = _packageApplication.GetSingleJobThicknessWeight(j);
			return jd.depthOf1Job;
		}

		/*
		  PickUp        : "CompData"
		  A4            : "Dispatch_BW"
		  AddressLabel  : "CompData"
          Samples       : "CompData"
          CustomerLogo  : "Logo_Labels_FG"
          PayMe         : "CompData"
          AustraliaPost : "CompData"

			if (!AccessAllowed(order))
			{
				return Forbid();
			}
			var orderDto = GetOrderPrintDto(order);

			var jsonStr = JsonConvert.SerializeObject(orderDto);

			Report report = new Report();
			var fileName = _config["Reports:LepQuote"];
			report.Load(fileName);

			report.Dictionary.Connections[0].ConnectionString = "Json='" + jsonStr + "'";
			report.Prepare();

			PDFSimpleExport pdfExport = new PDFSimpleExport();

			using (MemoryStream ms = new MemoryStream())
			{
				pdfExport.Export(report, ms);
				return new FileContentResult(ms.ToArray(), "application/pdf")
				{
					FileDownloadName = $"{orderDto.Title}-{order.Id}.pdf"
				};
			}
		*/

		//[HttpGet("Order/{OrderId}/CreateConsignment/{Facility}/{CarrierName}/{ServiceName}/{AccountNumber}")]
		//public IActionResult CreateConsignment([FromServices] ICourierApplication courierApp,
		//	[FromRoute] int OrderId, [FromRoute] Facility? Facility, [FromRoute] string CarrierName, [FromRoute]string ServiceName, [FromRoute] string AccountNumber
		//	)
		//{
		//	if (OrderId == 0 || Facility == null || string.IsNullOrEmpty(CarrierName) || string.IsNullOrEmpty(ServiceName) || string.IsNullOrEmpty(AccountNumber))
		//	{
		//		return BadRequest();
		//	}

		//	var order = _orderApp.GetOrder(OrderId);
		//	var result = courierApp.CreateConsignment(order, Facility.Value, CarrierName, ServiceName, AccountNumber);
		//	return new OkObjectResult(result);
		//}
	}
}
