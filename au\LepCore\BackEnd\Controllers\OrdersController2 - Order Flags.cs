using lep;
using lep.address.impl;
using lep.configuration;
using lep.extensionmethods;
using lep.job;
using lep.order;
using lep.user;

using System;
using System.Dynamic;
using System.Linq;

namespace LepCore.Controllers
{
	public partial class OrdersController
	{
		protected dynamic GetFlagsRelatedToOrder(IOrder order)
		{
			// var vis = new Dictionary<string, object>();
			dynamic vis = new ExpandoObject();

			var adminAndPrepress = new[] { Role.SuperAdministrator, Role.Administrator, Role.Prepress };
			Func<Role[], bool> allowAccess = roles =>
			{
				if (_currentUser != null && _currentUser is IStaff)
					for (var i = 0; i < roles.Length; i++) if (((IStaff)_currentUser).Role == roles[i]) return true;
				return false;
			};

			var isAdminAndPrepress = allowAccess(new Role[] { Role.Administrator, Role.Prepress });

			var orderIsNotNew = order.Id > 0;

			var emptyOrderOrHasSomeUnpackedJobs = order.Jobs.Any() ||
												  order.Jobs.All(j => (int)j.Status < (int)JobStatusOptions.Packed);
			vis.addressPnl = emptyOrderOrHasSomeUnpackedJobs;

			var canUpdate = order.CanUpdate(_currentUser);
			var canWithdraw = order.CanWithdraw(_currentUser);
			//var canReactiavte = order.CanReactivate(_currentUser);

			vis.resetAddress = canUpdate && emptyOrderOrHasSomeUnpackedJobs && !order.PackDetail.IsCustom;

			if (!order.Courier.IsNone && !order.Courier.IsPickup)
			{
				// Not hard coding 50% discount msg
				string strfreightMsg;
				var margin = _freightApplication.GetCustomerFreightMarginFromCode(order.FreightPriceCode);
				if (margin != 0)
					strfreightMsg =
						$"After applying freight price code '{order.FreightPriceCode}' {margin}% discount : {order.Courier}";
				else strfreightMsg = $"Freight (inc handling) : {order.Courier}";

				vis.freightMsgText = strfreightMsg;
			}
			else if (!order.Courier.IsNone && order.Courier.IsPickup)
			{
				vis.freightMsgText = $"{order.Courier}";
			}

			if (order.Promotion != null && order.PromotionBenefit > 0)
			{
				vis.promoBenifit = $"'{order.Promotion.PromotionCode}' : {order.Promotion.ShortDescription}";
				vis.promoBenifitAmount = order.PromotionBenefit;
			}

			// for Customer
			// from www\customer\order-view2.aspx
			if (_currentUser is ICustomerUser)
			{
				if (order.IsQuote) vis.deleteButton = false;
				else vis.deleteButton = orderIsNotNew && order.Status == OrderStatusOptions.Open && canUpdate;

				//if (!order.Enable) {
				if (((ICustomerUser)_currentUser).PaymentTerms == PaymentTermsOptions.COD)
				{
					//	vis.deleteButton = false;
				}
				//}

				vis.printButton = orderIsNotNew &&
								  (order.Status == OrderStatusOptions.Submitted || order.Status == OrderStatusOptions.Ready);

				vis.withdrawButton = orderIsNotNew && canWithdraw;
				//vis.reactivateButton = orderIsNotNew && order.CanReactivate(_currentUser);

				if (order.Status == OrderStatusOptions.Open /*&& order.Enable*/)
				{
					vis.addButton = canUpdate;
					vis.submitButton = true;

					if (order.Jobs.Count() == 0) vis.submitButton = false;
					else if (order.PriceOfJobs.HasValue && order.Courier.IsNone) vis.submitButton = false;
					else if (order.PriceOfJobs.HasValue &&
							 order.Jobs.Any(job => !job.IsArtworkValidForSubmit() || job.IsQuoteExpired || job.HasReject))
						vis.submitButton = false;

					vis.saveButton = canUpdate;
					vis.addressPnl = true;
					vis.resetAddress = true;
					vis.editAddressBtn = true;
					vis.contactPnl = true;
				}
				else
				{
					vis.addButton = false;
					vis.submitButton = false;
					vis.saveButton = false;
					vis.addressPnl = false;
					vis.resetAddress = false;
					vis.editAddressBtn = false;
					vis.contactPnl = false;
				}

				if (((ICustomerUser)_currentUser).PaymentTerms == PaymentTermsOptions.COD &&
					order.Status > OrderStatusOptions.Open)
				{
					vis.deleteButton = false;
					//vis.withdrawButton = false;
					vis.addressPnl = false;
				}

				if (((ICustomerUser)_currentUser).PaymentTerms == PaymentTermsOptions.COD &&
					order.Status == OrderStatusOptions.Open && order.Jobs.Any(j => /*j.Enable &&*/ j.HasReject))
				{
					vis.deleteButton = false;
					vis.withdrawButton = false;
				}

				if (((ICustomerUser)_currentUser).PaymentTerms == PaymentTermsOptions.Account &&
					order.Status < OrderStatusOptions.Dispatched)
				{
					vis.addressPnl = true;
					vis.resetAddress = true;
				}

				if (order.PackDetail.IsCustom)
				{
					vis.addressPnl = false;
					vis.resetAddress = false;
				}

				//vis.promotionNotValidInst = (bool)vis.submitButton && order.Promotion != null &&
				//							!_promotionApplication.IsPromotionValid(order, new StringBuilder(), out benefit);

				// MakeAddressEditableForOnAccountCustomers
				if (//order.PaymentStatus != OrderPaymentStatusOptions.Paid &&
					(int)order.Status < (int)OrderStatusOptions.Finished &&
					order.Customer.PaymentTerms == PaymentTermsOptions.Account)
				{
					vis.addressPnl = true;
					vis.saveButton = true;
				}
				// MakeAddressEditableForCODCustomers
				if (order.PaymentStatus != OrderPaymentStatusOptions.Paid &&
					(int)order.Status < (int)OrderStatusOptions.Submitted &&
					order.Customer.PaymentTerms == PaymentTermsOptions.COD)
				{
					vis.addressPnl = true;
					vis.saveButton = true;
				}

				if (order.PackDetail.IsCustom)
				{
					vis.addressPnl = false;
				}

				if ((bool)vis.submitButton && order.Jobs.Any(_ => _.QuoteNeedApprove))
				{
					vis.approveQuoteAndSubmitButton = true;
					vis.submitButton = false;
				}
				// end of customer
				if (order.Payments != null && order.Payments.Any())
				{
					vis.deleteButton = false;
				}
			}

			if (_currentUser is IStaff)
			{
				string userAgent = HttpContext.Request.Headers["User-Agent"];
				if (order.Id > 0)
					if (userAgent.ToLower().IndexOf("mac") != -1)
					{
						if (order.Status == OrderStatusOptions.Archived)
							vis.artworkFolderNavigateUrl =
								$"lep:mount:{LepGlobal.Instance.ArtworkArchiveDirectoryMac}/{order.DateCreated:yyyyMMdd}/{order.OrderNr}".Replace(" ",
									"%20");
						else
							vis.artworkFolderNavigateUrl =
								$"lep:mount:{LepGlobal.Instance.ArtworkDirectoryMac}/{order.DateCreated:yyyyMMdd}/{order.OrderNr}".Replace(" ", "%20");
						vis.artworkFolderTarget = "";
						vis.artworkFolderVisible = true;
					}
					else
					{
						// IE/win32 gets a file explorer link
						if (order.Status == OrderStatusOptions.Archived)
							vis.artworkFolderNavigateUrl =
								$"{LepGlobal.Instance.ArtworkArchiveDirectoryPC}/{order.DateCreated:yyyyMMdd}/{order.OrderNr}";
						else
							vis.artworkFolderNavigateUrl = $"{LepGlobal.Instance.ArtworkDirectoryPC}/{order.DateCreated:yyyyMMdd}/{order.OrderNr}";
						vis.artworkFolderTarget = "_artwork";
						vis.artworkFolderVisible = true;
					}

				//vis.artworkFolderNavigateUrl = LepGlobal.Instance.ArtworkDirectory(order).FullName;

				vis.quoteApproveChkVisible = true;

				// can staff delete order?
				if (order.IsQuote) vis.deleteButton = false;
				else
					vis.deleteButton = orderIsNotNew && order.Status == OrderStatusOptions.Open && isAdminAndPrepress && canUpdate;

				vis.withdrawButton = orderIsNotNew && canWithdraw && isAdminAndPrepress && canUpdate;

				//vis.reactivateButton = orderIsNotNew && isAdminAndPrepress && canReactiavte;

				if (order.Status == OrderStatusOptions.Open /*&& order.Enable*/)
				{
					vis.addButton = isAdminAndPrepress && canUpdate;
					vis.jobtypelst = isAdminAndPrepress && canUpdate;
					vis.submitButton = order.Jobs.Any() && !order.Courier.IsNone && order.Jobs.Any(j => j.Price != "");
					vis.contactPnl = true;
				}
				else
				{
					vis.addButton = false;
					vis.jobtypelst = false;
					vis.submitButton = false;
					vis.contactPnl = false;
					vis.jobtypelst = false;
				}

				vis.addressPnl = order.Jobs.Count() > 0 ||
								 order.Jobs.All(j => (int)j.Status < (int)JobStatusOptions.Packed);
				vis.resetAddress = vis.addressPnl;
				vis.saveButton = vis.addressPnl;

				/*
						//MikeGreenan end quick hack
						//MikeGreenan customer logos, using virtual directory in IIS
						string custLogoFile = Server.MapPath("~/customerlogos") + "\\" + order.Customer.Username + ".jpg";
						if (File.Exists(custLogoFile))
							{
							calChk.Enabled = true;
							}
				 */

				//MikeGreenan end customer logos

				decimal limit;
				decimal.TryParse(_configApp.GetValue(lep.configuration.Configuration.OrderLimit), out limit);
				var isReady = true;
				if (order.Status == OrderStatusOptions.Submitted/* && order.Enable*/ && order.Price.HasValue)
					foreach (var j in order.Jobs)
						if (/*j.Enable && */(j.Status != JobStatusOptions.PreflightDone || j.Status != JobStatusOptions.DPCPreProduction))
						{
							isReady = false;
							break;
						}
						else isReady = false;
				var isExpensive = order.Price.HasValue && order.Price.Value > limit;

				//TODO MikeGreenan
				// vis.readyButton = IsReady && order.Enable && order.Status == OrderStatusOptions.Submitted && !IsExpensive && AllowAccess(adminAndPrepress);
				vis.readyButton = true;

				vis.ReadyInst = isReady && !isExpensive;
				vis.ExpensiveInst = isReady && isExpensive;
				vis.returnButton = order.CanReturn && allowAccess(adminAndPrepress);

				/*  protected void MakeAddressEditableForOnAccountCustomers()  */
				if (order.PaymentStatus != OrderPaymentStatusOptions.Paid &&
					(int)order.Status < (int)OrderStatusOptions.Finished &&
					order.Customer.PaymentTerms == PaymentTermsOptions.Account)
				{
					vis.addressPnl = true;
					vis.saveButton = true;
				}

				vis.saveButton = emptyOrderOrHasSomeUnpackedJobs;
				vis.approveButton = isReady /*&& order.Enable */&& order.Status == OrderStatusOptions.Submitted && isExpensive &&
									allowAccess(new Role[] { Role.Administrator });

				//MikeGreenan end customer logos
				var rdyArtworkInstVisible = order.Jobs.Where(j => j.Status < JobStatusOptions.Dispatched)
					.Aggregate(false, (current, job) => current | job.Artworks.Any(art => !string.IsNullOrEmpty(art.Supplied) &&
																						  string.IsNullOrEmpty(art.Ready) &&
																						  job.SupplyArtworkApproval !=
																						  JobApprovalOptions.Rejected));

				vis.RdyArtworkInst = rdyArtworkInstVisible;

				var showApprovalInst = order.Jobs.Any(j => j.NeedApproval || j.HasReject || !j.IsArtworkValidForSubmit());
				vis.ApprovalInst = order.CanReturn && showApprovalInst;

				vis.NoActionInst = order != null && order.Id > 0 && rdyArtworkInstVisible == false &&
								   (bool)vis.ApprovalInst == false &&
								   (bool)vis.ReadyInst == false && (bool)vis.ExpensiveInst == false;


			

			}

			DateTime? dispatchEst = null;

			var estimated = true;
			dispatchEst = order.DispatchEst;    //_orderApp.GetDispatchDate(order, out estimated);
			if (dispatchEst == null)
			{
				vis.estPnl = false;
			}
			else
			{
				vis.estPnl = true;
				if (estimated == true) vis.minDayText = "Estimated Dispatch " + dispatchEst.Value.ToString("dd-MMM-yyyy HH:mm");
				else vis.minDayText = "Agreed Dispatch " + dispatchEst.Value.ToString("dd-MMM-yyyy HH:mm");
			}

			vis.projectedEstimatedDispatchDate = null;
			if (order.Status < OrderStatusOptions.Submitted)
			{
				var estimated2 = true;
				var dispatchEst2 = _orderApp.GetDispatchDate(order, DateTime.Now, out estimated2);    //

				if (dispatchEst2 != null)
				{
					vis.projectedEstimatedDispatchDate = dispatchEst2.Value.ToString("dd-MMM-yyyy HH:mm");
				}
			}

			vis.copyButton = true;

			vis.custAttachLogoChkEnabled = _userApplication.GetCustomerLogo(order.Customer.Username) != null;

			vis.Promo = true;
			var ignoreOrderStatus = _currentUser is IStaff && ((IStaff)_currentUser).Role == Role.Administrator;
			if (/*!order.Enable ||*/
				order.Status != OrderStatusOptions.Open && order.Status != OrderStatusOptions.Submitted && !ignoreOrderStatus)
				vis.Promo = false;

			vis.reorder = order.Status.Is(OrderStatusOptions.Archived, OrderStatusOptions.Dispatched, OrderStatusOptions.Finished) &&
						  order.Jobs.All(_jobApp.ReadyArtExists)
						  && order.Jobs.All(j => !j.IsWideFormat())
						  ;
			//vis.reorder = true;

			if (order.IsQuote)
			{
				vis.promotionPnlVisible = false;
				vis.quotePnlVisible = true;
				vis.promoBenifitTrVisible = false;
			}
			else
			{
				vis.promotionPnlVisible = true;
				vis.quotePnlVisible = false;
				vis.promoBenifitTrVisible = true;
			}

			vis.DefaultAddress = order?.Customer?.PostalAddress;

			if (_currentUser is ICustomerUser)
			{
				vis.currentDeliveryDetailsIsInFavourite = (_currentUser as ICustomerUser).FavouriteDeliveryDetails?.Contains(
															  new DeliveryDetails()
															  {
																  Address = (PhysicalAddress)order.DeliveryAddress,
																  RecipientName = order.RecipientName,
																  RecipientPhone = order.RecipientPhone
															  }
														  ) ?? false;
			}

			if (_currentUserIsAnonymousWLCustomer && order.IsWLOrder && order.IsWLOrderPaidFor)
			{
				vis.saveButton = false;
				vis.deleteButton = false;
				vis.addressPnl = false;
				vis.addButton = false;
			}

			if (_currentUserIsLoggedInWLCustomer && order.IsWLOrder && order.IsWLOrderPaidFor)
			{
				vis.saveButton = false;
				vis.deleteButton = false;
				vis.addressPnl = false;
				vis.addButton = false;
			}

			vis.ExtraFiles = _cachedAccessTo.OrderExtraFiles(order);
			vis.PackWithoutPallets = _packageApplication.IsSkidPossible(order);
			vis.NoLooseCarton = order.Customer.FreightPriceCode == "F1";
			//vis.PackPalletized = order.pa

			try
			{
				vis.splitDeliveryPossible = order.Status == OrderStatusOptions.Open && order.Jobs.Count == 1 &&
						   (order.Jobs[0].IsBrochure() || order.Jobs[0].IsMagazine() || order.Jobs[0].IsMagazineSeparate());
				vis.splitCount = order.Jobs[0]?.Splits?.Count ?? 0;
			} catch { }


			try
			{
				if (order.Courier.IsPickup 
					|| order.PackDetail.FGCourier.IsPickup
					|| order.PackDetail.PMCourier.IsPickup
					)
				{
					
					vis.PODReqd = true;
				}
			}
			catch { }

			return vis;
		}
	}
}
