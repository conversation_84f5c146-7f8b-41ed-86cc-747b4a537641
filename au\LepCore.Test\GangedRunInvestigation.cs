using System;
using System.Linq;
using lep.barcode;
using lep.job;
using lep.run;
using lep.jobmonitor;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Collections.Generic;

namespace LepCore.Test
{
    /// <summary>
    /// Investigation tool for ganged digital runs disappearing from job boards when scanned as DPC Complete
    /// Run Numbers: 375367, 375335
    /// </summary>
    [TestClass]
    public class GangedRunInvestigation
    {
        private static LepFixture lep;

        [ClassInitialize]
        public static void ClassInitialize(TestContext context)
        {
            lep = new LepFixture();
        }

        [TestMethod]
        public void Investigate_GangedRuns_375367_375335()
        {
            Console.WriteLine("=== INVESTIGATING GANGED DIGITAL RUNS DISAPPEARING ===");
            Console.WriteLine("Run Numbers: 375367, 375335");
            Console.WriteLine();

            var runIds = new[] { 375367, 375335 };

            foreach (var runId in runIds)
            {
                InvestigateRun(runId);
                Console.WriteLine();
            }
        }

        private void InvestigateRun(int runId)
        {
            Console.WriteLine($"--- INVESTIGATING RUN {runId} ---");

            try
            {
                var run = lep.RunApp.GetRun(runId);
                if (run == null)
                {
                    Console.WriteLine($"ERROR: Run {runId} not found!");
                    return;
                }

                Console.WriteLine($"Run Status: {run.Status}");
                Console.WriteLine($"Print Type: {run.PrintType}");
                Console.WriteLine($"Created: {run.CreatedDate}");
                Console.WriteLine($"Modified: {run.ModifiedDate}");
                Console.WriteLine($"Job Count: {run.Jobs.Count}");
                Console.WriteLine($"Active Job Count: {run.ActiveJobs.Count}");

                // Check if this is a ganged digital run
                var isDigital = run.PrintType == lep.job.PrintType.D;
                var isGanged = run.Jobs.Count > 1;
                Console.WriteLine($"Is Digital: {isDigital}");
                Console.WriteLine($"Is Ganged: {isGanged}");

                if (isDigital && isGanged)
                {
                    Console.WriteLine("*** THIS IS A GANGED DIGITAL RUN ***");
                }

                Console.WriteLine();
                Console.WriteLine("Jobs in this run:");
                foreach (var job in run.Jobs)
                {
                    AnalyzeJob(job);
                }

                // Test job board eligibility
                Console.WriteLine();
                Console.WriteLine("Job Board Eligibility Analysis:");
                foreach (var job in run.Jobs)
                {
                    AnalyzeJobBoardEligibility(job);
                }

                // Simulate DPC Complete scanning
                Console.WriteLine();
                Console.WriteLine("=== SIMULATING DPC COMPLETE SCAN ===");
                SimulateDPCCompleteScan(run);

            }
            catch (Exception ex)
            {
                Console.WriteLine($"ERROR investigating run {runId}: {ex.Message}");
            }
        }

        private void AnalyzeJob(IJob job)
        {
            Console.WriteLine($"  Job {job.Id}: {job.Name}");
            Console.WriteLine($"    Status: {job.Status} ({(int)job.Status})");
            Console.WriteLine($"    Customer: {job.Customer.Name}");
            Console.WriteLine($"    Print Type: {job.PrintType}");
            Console.WriteLine($"    Template: {job.Template?.Name ?? "N/A"}");
            Console.WriteLine($"    Enabled: {job.Enable}");
            Console.WriteLine($"    Has Reject: {job.HasReject}");
            Console.WriteLine($"    Scan Count: {job.ScanCount}");
            Console.WriteLine($"    Modified: {job.ModifiedDate}");

            // Check if it's a business card (common for ganged runs)
            var isBusinessCard = job.Template?.Name?.Contains("Business Card") == true;
            Console.WriteLine($"    Is Business Card: {isBusinessCard}");

            // Check digital and ganged status
            var isDigitalAndRunGanged = job.IsDigitalAndRunGanged();
            Console.WriteLine($"    Is Digital and Run Ganged: {isDigitalAndRunGanged}");
        }

        private void AnalyzeJobBoardEligibility(IJob job)
        {
            // Based on JobBoardEventsConsumer.ProcessJob logic
            var boardsToAppearIn = JobBoardSelectionHelper.GetBoardsToAppearIn(job);
            var isEligibleForBeingInBoard = (boardsToAppearIn.Count() > 0)
                                                && job.Enable
                                                && !job.HasReject
                                                && job.SupplyArtworkApproval == JobApprovalOptions.NotNeeded
                                                && job.ReadyArtworkApproval == JobApprovalOptions.NotNeeded
                                                && job.ProofStatus == JobProofStatus.None
                                                && !job.NeedApproval;

            Console.WriteLine($"  Job {job.Id} Job Board Eligibility:");
            Console.WriteLine($"    Boards to appear in: {boardsToAppearIn.Count()}");
            Console.WriteLine($"    Enabled: {job.Enable}");
            Console.WriteLine($"    Has Reject: {job.HasReject}");
            Console.WriteLine($"    Supply Artwork Approval: {job.SupplyArtworkApproval}");
            Console.WriteLine($"    Ready Artwork Approval: {job.ReadyArtworkApproval}");
            Console.WriteLine($"    Proof Status: {job.ProofStatus}");
            Console.WriteLine($"    Need Approval: {job.NeedApproval}");
            Console.WriteLine($"    ELIGIBLE FOR JOB BOARD: {isEligibleForBeingInBoard}");

            if (!isEligibleForBeingInBoard)
            {
                var reasons = new List<string>();
                if (boardsToAppearIn.Count() == 0) reasons.Add("No boards to appear in");
                if (!job.Enable) reasons.Add("Job disabled");
                if (job.HasReject) reasons.Add("Job has reject");
                if (job.SupplyArtworkApproval != JobApprovalOptions.NotNeeded) reasons.Add("Supply artwork approval needed");
                if (job.ReadyArtworkApproval != JobApprovalOptions.NotNeeded) reasons.Add("Ready artwork approval needed");
                if (job.ProofStatus != JobProofStatus.None) reasons.Add("Proof status not none");
                if (job.NeedApproval) reasons.Add("Needs approval");

                Console.WriteLine($"    REASONS NOT ELIGIBLE: {string.Join(", ", reasons)}");
            }
        }

        private void SimulateDPCCompleteScan(IRun run)
        {
            Console.WriteLine($"Simulating DPC Complete scan for run {run.Id}...");

            try
            {
                // This simulates what happens in BarcodeApplication.Scan when scanning "DPCComplete" for a run
                var scanner = "DPCComplete";
                var barcode = $"R{run.Id}";

                Console.WriteLine($"Barcode: {barcode}");
                Console.WriteLine($"Scanner: {scanner}");

                // Check if DPCComplete is in the job status list (it should be)
                var jobStatusList = new List<string> { "dpcpreproduction", "dpcprinted", "dpccomplete" };
                var isJobStatus = jobStatusList.Contains(scanner.ToLower());

                Console.WriteLine($"Is Job Status: {isJobStatus}");

                if (isJobStatus)
                {
                    Console.WriteLine("This will update ALL jobs in the run to DPCComplete status");
                    
                    var jstatus = JobStatusOptions.DPCComplete;
                    Console.WriteLine($"Target Job Status: {jstatus} ({(int)jstatus})");

                    foreach (var job in run.ActiveJobs)
                    {
                        Console.WriteLine($"  Job {job.Id} would be updated from {job.Status} to {jstatus}");
                        
                        // Check if this status change would make the job ineligible for job board
                        var wouldBeEligibleAfterScan = CheckJobBoardEligibilityAfterStatusChange(job, jstatus);
                        Console.WriteLine($"    Would be eligible for job board after scan: {wouldBeEligibleAfterScan}");
                        
                        if (!wouldBeEligibleAfterScan)
                        {
                            Console.WriteLine($"    *** JOB {job.Id} WOULD BE REMOVED FROM JOB BOARD ***");
                        }
                    }
                }

                // Check run status update
                Console.WriteLine();
                Console.WriteLine("Run status would also be updated based on job statuses");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ERROR simulating DPC Complete scan: {ex.Message}");
            }
        }

        private bool CheckJobBoardEligibilityAfterStatusChange(IJob job, JobStatusOptions newStatus)
        {
            // Simulate the job with the new status for eligibility check
            // Note: We can't actually change the job status here, so we'll check the logic manually

            // Based on the routing logic in StandardRouting.cs
            // DPCComplete (status 23) might be a completion status that removes jobs from job board

            // Check if the new status is a "completion" status
            var completionStatuses = new[] { 
                JobStatusOptions.Packed,      // 28
                JobStatusOptions.Dispatched,  // 29  
                JobStatusOptions.Complete     // 30
            };

            var isDPCComplete = newStatus == JobStatusOptions.DPCComplete; // 23

            // If it's DPCComplete, it might still be eligible depending on the routing
            // But if it's a completion status, it would definitely be removed

            if (completionStatuses.Contains(newStatus))
            {
                return false; // Definitely removed from job board
            }

            if (isDPCComplete)
            {
                // DPCComplete might or might not be eligible - depends on the specific routing logic
                // From the code, it seems like DPCComplete jobs might be removed from job boards
                Console.WriteLine($"      DPCComplete status - may cause removal from job board");
                return false; // Likely removed
            }

            return true; // Would still be eligible
        }

        [TestMethod]
        public void Test_DPCComplete_JobBoard_Logic()
        {
            Console.WriteLine("=== TESTING DPC COMPLETE JOB BOARD LOGIC ===");

            // Test the specific logic that determines if a job should be on the job board
            // when it has DPCComplete status

            var testJobIds = new[] { 
                // Add some job IDs from the runs if you know them
                // These would need to be actual job IDs from the database
            };

            foreach (var jobId in testJobIds)
            {
                try
                {
                    var job = lep.JobApp.GetJob(jobId);
                    if (job != null)
                    {
                        Console.WriteLine($"Job {jobId}: Status = {job.Status}");
                        AnalyzeJobBoardEligibility(job);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error testing job {jobId}: {ex.Message}");
                }
            }
        }
    }
}
