#define DB

using BenchmarkDotNet.Attributes;
using lep;
using lep.despatch;
using lep.despatch.impl;
using lep.despatch.impl.label;
using lep.freight;
using lep.job;
using lep.job.impl;
using lep.order;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using Order = lep.order.impl.Order;

namespace LepCore.Test
{

	[TestClass]
	public class PackTest
	{
		public const string _150_GSM_Gloss_Art = "150 GSM Gloss Art";
		private const string SkidORTurducken = "CHECK SKID/Turduken";

		public LepFixture lep { get; private set; }

		[TestInitialize]
		public void Init()
		{
			this.lep = new LepFixture();
		}



		public PackTest()
		{


		}


		/*
		J/N 1674827 2000 DL Magnets.  Packing Info says 2 x A4 of Qty 700/6kg each plus 1 x A4 of Qty 600/5,2kg
		Actual packing was 
		1 x A4 @1080 @8.9kg 
		1 x A4 @ 920 @ 7.7kg
		GOOD
		
		
		Currently
	    Order: 1211387
		Job : 1674827, 'Brochures & Flyers',  'O', Qty: 2000,  300 GSM Matt Art,   DL(99x 210) , no fold ((x )), 0 
		Turducken (Oversized A3)   16.6kg ...
        A4          @ 920 each,   7.7kg  ******** 
        A4          @ 1080 each,   8.9kg  ******** 
		Total:  1 Turducken (Oversized A3). 2 A4.
		*/

		[TestMethod]
		public void LORD_1243_1_Order_1211387_1674827()
		{
			var result = PackOrder(1211387);
			Debug.WriteLine(JsonConvert.SerializeObject(result, Formatting.Indented));

		}


		/*
		 Hi John,
		This is a DPC  A4, 300 gloss, cello front with 2 magnets. The packing it gives is A4 small, 9.8kg
		J/N 1661267 A4 Small 9.8kg Qty 500
		The true packing is 2 x A4 @ 6.5 & 6.25kg.

		2 Magnets???
		Currently
		A4 Small    @ 500 each,   9.8kg 
    	*/
		[TestMethod]
		public void LORD_1243_2_Order_1202306_1661267()
		{
			var result = PackOrder(1202306);
			Debug.WriteLine(JsonConvert.SerializeObject(result, Formatting.Indented));

		}


		/*
		Order # 1199652 was originally packed at 
		 3 @ 2250,
		 1 @ 1250 on 300 gloss. 
		They managed 1,300 in a A4 but not sure how they pack. 
		The girls do bundles of 120  alternated every 15. 
		9 bundles at 120 would fit into an A4, total 1080.		 

		currently
		Order: 1199652
		Job : 1656980, 'DL Calendars',  'O', Qty: 8000,  300 GSM Gloss Art,   DL(99x 210) , no fold ((x )), 0 


		Skid - Medium   75.2kg ...
			 1 A4 Small   @ 90 each,  1.1Kg , ********
			 7 A4         @ 1130 each,  9.3Kg , ********

		Total:  1 Skid - Medium. 1 A4 Small. 7 A4.
		*/

		[TestMethod]
		public void LORD_1243_3_Order_1199652()
		{
			var result = PackOrder(1199652);
			Debug.WriteLine(JsonConvert.SerializeObject(result, Formatting.Indented));

		}

		[TestMethod]
		public void LORD_1298_Job_1697548()
		{
			var result = PackOrder(1233982);
			Debug.WriteLine(JsonConvert.SerializeObject(result, Formatting.Indented));

		}

		[TestMethod]
		public void LORD_1233997()
		{
			var result = PackOrder(1233997);
			Debug.WriteLine(JsonConvert.SerializeObject(result, Formatting.Indented));

		}

		[TestMethod]
		public void LORD_1234014()
		{
			var result = PackOrder(1234014);
			Debug.WriteLine(JsonConvert.SerializeObject(result, Formatting.Indented));

		}


		[TestMethod]
		public void ********()
		{
			var result = PackOrder(1226511);
			Debug.WriteLine(JsonConvert.SerializeObject(result, Formatting.Indented));

		}


		//test method for order 1230354
		[TestMethod]
		public void PackOrder1230354()
		{
			var result = PackOrder(1230354);
			Debug.WriteLine(JsonConvert.SerializeObject(result, Formatting.Indented));
		}

		// new test method

		//O# 1234430 says 58.2kgs  in fact its 79kgs. Going on a skid but will need to change weight.
		[TestMethod]
		public void PackOrder1234430()
		{
			Debug.WriteLine("Penny: O# 1234430 says 58.2kgs  in fact its 79kgs. Going on a skid but will need to change weight.");
			var result = PackOrder(1234430);
			//Debug.WriteLine(JsonConvert.SerializeObject(result, Formatting.Indented));
		}

		//O# 1235309 says total order  10.1kgs infact it is 20kgs. Will also need to change the weight.
		[TestMethod]
		public void PackOrder1235309()
		{
			Debug.WriteLine("Penny: O# 1235309 says total order  10.1kgs infact it is 20kgs. Will also need to change the weight.");
			var result = PackOrder(1235309);
			//Debug.WriteLine(JsonConvert.SerializeObject(result, Formatting.Indented));
		}

		//O# 	1234428 says total weight 109kgs when it is 158kgs. Will also change weight for courier.
		[TestMethod]
		public void PackOrder1234428()
		{
			Debug.WriteLine("Penny: O#	1234428 says total weight 109kgs when it is 158kgs. Will also change weight for courier.");
			var result = PackOrder(1234428);
			//Debug.WriteLine(JsonConvert.SerializeObject(result, Formatting.Indented));



		}

		[TestMethod]
		public void PackOrder1262303()
		{
			
			var result = PackOrder(1262303);
			Debug.WriteLine(JsonConvert.SerializeObject(result, Formatting.Indented));
		}
		

		[TestMethod]
		public void PackNotePadsPenny()
		{
			// call above 3 test methods to get the results.
			PackOrder1234430();
			PackOrder1235309();
			PackOrder1234428();
			
		}



		[TestMethod]
		public void ___TLP()
		{


			var job = lep.JobApp.GetJob(1972993);
			var tlp = job.TopLevelPackages();
			Assert.IsNotNull(tlp);

			var filename = Path.Combine(@"c:\Lepdata\0",
				string.Format("{0}_{1}_{2}.xps", DateTime.Now.Ticks, "FurtherPocessingThumbnailLabel", job.Id));

			var label = new FurtherPocessingThumbnailLabel();

			label.ConfigurationApplication = lep.ConfigApp;
			label.PrinterAndTray = "Microsoft XPS Document Writer";
			label.PrintFileName = filename;
			label.Job = job;
			label.SetupPrintProperties();
			label.Print();


			  filename = Path.Combine(@"c:\Lepdata\0",
				string.Format("{0}_{1}_{2}.xps", DateTime.Now.Ticks+1, "FurtherPocessingThumbnailLabel", job.Id));

		
			// Option 2: Using the unified label directly
			var label2 = new UnifiedFurtherProcessingLabel(
				job,
				LabelType.FurtherProcessing,
				"Microsoft XPS Document Writer",
				lep.ConfigApp,
				filename
				);
			
			label2.SetupPrintProperties();

	 

			label2.ShowPreview();
		}






		[TestMethod]
		public void LORD_1243_1()
		{
			var job = new Job
			{
				Quantity = 2000,
				PrintType = PrintType.O,
				Template = new JobTemplate() { Id = (int)JobTypeOptions.Brochure, Name = "Brochure" },
				Facility = Facility.FG,
				//FinishedSize = new Size() { PaperSize = new PaperSize() { Name = "A4" }, Width = 210, Height = 297 },
				//Magnet = true,

				//DL  99  210
				FinishedSize = new Size() { PaperSize = new PaperSize() { Name = "DL" }, Width = 99, Height = 210 },
				//Stock = new Stock() { Name = _150_GSM_Gloss_Art, GSM = 150, Thickness = 0.11m },
				//46	300 GSM Matt Art	 1	Y	Y	0.32	Y
				Stock = new Stock() { Id = 46, Name = "300 GSM Matt Art", GSM = 300, Thickness = 0.32m },
			};

			var order = new Order();
			order.Jobs.Add(job);
			job.Order = order;

			lep.PackageApp.SetPackage(job);
			lep.PackageApp.SetPackage(order);

			var result = order.PackDetail.FGPackageJson;
			Debug.WriteLine(JsonConvert.SerializeObject(result, Formatting.Indented));


		}






		//[TestMethod]
		public void ____DiffTest()
		{
			var o = lep.OrderApp.GetOrder(1209160);



			o.DeliveryInstructions = "Delivery on date " + DateTime.Now.AddDays(4).ToShortDateString();
			foreach (var j in o.Jobs)
			{

				var x = j.Id;
			}


			lep.OrderApp.Save(o);




			//var compareLogic = new CompareLogic();
			//compareLogic.Config.IgnoreCollectionOrder = true;
			//compareLogic.Config.MaxDifferences = int.MaxValue;
			//ComparisonResult result = compareLogic.Compare(o, o1);
			////o.Dump();


			//UserFriendlyReport friendlyReport = new UserFriendlyReport();
			//Debug.WriteLine(friendlyReport.OutputString(result.Differences));


		}




		ListOfPackages PackOrder(int id)
		{
			var order = lep.OrderApp.GetOrder(id);
			if (order == null)
			{
				throw new Exception("Order not found");
			}

			Debug.WriteLine($"Order: {order.Id}");

			foreach (var job in order.Jobs)
			{
				var template = ((JobTypeOptions)job.Template.Id).ToDescription();

				

				Debug.WriteLine(
					$" Job : {job.Id},   '{template}',  '{job.PrintType}', Qty: {job.Quantity},  {job.Stock?.Name ?? ""},   {job.FinishedSize?.PaperSize?.Name}({job.FinishedSize?.Width}x {job.FinishedSize?.Height}) , {job.FoldedSize?.PaperSize?.Name ?? "no fold"} (({job.FoldedSize?.Width}x {job.FoldedSize?.Height})), {job.Pages} ");
				lep.PackageApp.SetPackage(job);
			}


			lep.PackageApp.SetPackage(order);
			var result = new ListOfPackages();
			result.AddRange(order.PackDetail.FGPackageJson);
			result.AddRange(order.PackDetail.PMPackageJson);

			Debug.WriteLine("\n" + result.ToString());

			//Debug.WriteLine(JsonConvert.SerializeObject(result, Formatting.Indented));

			Debug.WriteLine(@"
------------------------------------------------------------------





");
			//GC.Collect();
			return result;
		}





		//[Benchmark]
		//[TestMethod]
		public void _____BC_packing1_420GSM()
		{
			/*
			17	310 GSM Deluxe Artboard	0.335
			93	360 GSM Deluxe Artboard	0.387
			91	420 GSM Deluxe Artboard	0.459
			*/
			var stocks = lep.JobApp.ListStock()
				.Where(_ => _.GSM == 420 /*||	 _.GSM == 360 || _.GSM == 420*/)
				.OrderByDescending(_ => _.GSM).ToList();



			foreach (var stock in stocks)
				foreach (var quantity in new List<int>() { 100, 250, 500, 1000, 1500, 2000, 2500, 5000, 10000 })
				{
					var job = new Job
					{
						PrintType = PrintType.D,
						Template = new JobTemplate() { Id = (int)JobTypeOptions.BusinessCard },
						Quantity = quantity,
						Facility = Facility.FG,
						FinishedSize = new Size()
						{
							PaperSize = new PaperSize() { Name = "Business Card (90 x 55)" },
							Width = 90,
							Height = 55
						},
						Stock = stock
					};

					var order = new Order();
					order.Jobs.Add(job);
					job.Order = order;


					//job.FrontCelloglaze = JobCelloglazeOptions.None; job.BackCelloglaze = JobCelloglazeOptions.None;
					//DoTest(order);

					//job.FrontCelloglaze = JobCelloglazeOptions.Gloss; job.BackCelloglaze = JobCelloglazeOptions.None;
					//DoTest(order);

					//job.FrontCelloglaze = JobCelloglazeOptions.Gloss; job.BackCelloglaze = JobCelloglazeOptions.Gloss;
					//DoTest(order);

					//job.FrontCelloglaze = JobCelloglazeOptions.Matt; job.BackCelloglaze = JobCelloglazeOptions.None;
					//DoTest(order);

					job.FrontCelloglaze = JobCelloglazeOptions.Matt; job.BackCelloglaze = JobCelloglazeOptions.Matt;
					DoTest(order);

					//job.FrontCelloglaze = JobCelloglazeOptions.Gloss; job.BackCelloglaze = JobCelloglazeOptions.Matt;
					//DoTest(order);

					//job.FrontCelloglaze = JobCelloglazeOptions.Velvet; job.BackCelloglaze = JobCelloglazeOptions.None;
					//DoTest(order);

					//job.FrontCelloglaze = JobCelloglazeOptions.Velvet; job.BackCelloglaze = JobCelloglazeOptions.Velvet;
					//DoTest(order);
				}
			void DoTest(IOrder order)
			{
				//lep.PackageApp.LORD1254 = false;
				//lep.PackageApp.SetPackage(order.Jobs[0]);
				//lep.PackageApp.SetPackage(order);
				//var x = new ListOfPackages();
				//x.AddRange(order.PackDetail.FGPackageJson);



				lep.PackageApp.LORD1254 = true;
				lep.PackageApp.SetPackage(order.Jobs[0]);
				lep.PackageApp.SetPackage(order);
				var y = new ListOfPackages();
				y.AddRange(order.PackDetail.FGPackageJson);

				var job = order.Jobs[0];

				Debug.WriteLine(
					$"\nJob BC Qty: {job.Quantity},  {job.Stock?.Name ?? ""}, Cello: {job.FrontCelloglaze}/{job.BackCelloglaze} ");

				//Debug.WriteLine("\nbefore :\n" + new PackagePrinter(x).Result4());
				Debug.WriteLine("\nafter  :\n" + new PackagePrinter(y).Result4());

				Debug.WriteLine("\n------------------------------------\n");
			}

		}




	}
}
