-- Investigation: Ganged Digital Runs Disappearing from Job Boards when scanned as DPC Complete
-- Run Numbers: 375367, 375335
-- Issue: Runs may be disappearing from Job Boards when scanned as DPC Complete

-- Connection string for PRD03 (from appsettings):
-- Data Source=SRV03; user id=sa; password=*************; Initial Catalog=PRD_AU

USE PRD_AU;

-- 1. Check the specific runs mentioned
PRINT '=== INVESTIGATING SPECIFIC RUNS ===';
PRINT 'Run Numbers: 375367, 375335';

SELECT 
    r.Id as RunId,
    r.Status as RunStatus,
    r.PrintType,
    r.CreatedDate,
    r.ModifiedDate,
    COUNT(j.Id) as JobCount,
    STRING_AGG(CAST(j.Id as VARCHAR), ', ') as JobIds
FROM Run r
LEFT JOIN Job j ON j.RunId = r.Id
WHERE r.Id IN (375367, 375335)
GROUP BY r.Id, r.Status, r.PrintType, r.CreatedDate, r.ModifiedDate
ORDER BY r.Id;

-- 2. Check jobs in these runs and their current status
PRINT '';
PRINT '=== JOBS IN THESE RUNS ===';

SELECT 
    j.Id as JobId,
    j.RunId,
    j.Status as JobStatus,
    j.PrintType,
    j.Enable,
    j.HasReject,
    j.SupplyArtworkApproval,
    j.ReadyArtworkApproval,
    j.ProofStatus,
    j.NeedApproval,
    j.ModifiedDate,
    j.ScanCount,
    c.Name as CustomerName,
    j.Name as JobName
FROM Job j
INNER JOIN Customer c ON j.CustomerId = c.Id
WHERE j.RunId IN (375367, 375335)
ORDER BY j.RunId, j.Id;

-- 3. Check if these are digital ganged runs
PRINT '';
PRINT '=== DIGITAL GANGED RUN CHECK ===';

SELECT 
    r.Id as RunId,
    r.PrintType,
    CASE 
        WHEN r.PrintType = 'D' THEN 'Digital'
        WHEN r.PrintType = 'O' THEN 'Offset'
        ELSE 'Other'
    END as PrintTypeDescription,
    COUNT(j.Id) as JobCount,
    -- Check if jobs are business cards (common for ganged runs)
    COUNT(CASE WHEN jt.Name LIKE '%Business Card%' THEN 1 END) as BusinessCardCount
FROM Run r
LEFT JOIN Job j ON j.RunId = r.Id
LEFT JOIN JobTemplate jt ON j.JobTemplateId = jt.Id
WHERE r.Id IN (375367, 375335)
GROUP BY r.Id, r.PrintType;

-- 4. Check recent status changes for jobs in these runs
PRINT '';
PRINT '=== RECENT STATUS CHANGES ===';

SELECT TOP 50
    j.Id as JobId,
    j.RunId,
    j.Status as CurrentStatus,
    j.ModifiedDate,
    j.ScanCount,
    CASE 
        WHEN j.Status = 23 THEN 'DPCComplete'
        WHEN j.Status = 22 THEN 'DPCPrinted'
        WHEN j.Status = 3 THEN 'DPCPreProduction'
        ELSE CAST(j.Status as VARCHAR)
    END as StatusDescription
FROM Job j
WHERE j.RunId IN (375367, 375335)
ORDER BY j.ModifiedDate DESC;

-- 5. Check job board eligibility criteria for these jobs
PRINT '';
PRINT '=== JOB BOARD ELIGIBILITY CHECK ===';

SELECT 
    j.Id as JobId,
    j.RunId,
    j.Status,
    j.Enable,
    j.HasReject,
    j.SupplyArtworkApproval,
    j.ReadyArtworkApproval,
    j.ProofStatus,
    j.NeedApproval,
    CASE 
        WHEN j.Enable = 1 
             AND j.HasReject = 0 
             AND j.SupplyArtworkApproval = 0  -- NotNeeded
             AND j.ReadyArtworkApproval = 0   -- NotNeeded
             AND j.ProofStatus = 0            -- None
             AND j.NeedApproval = 0 
        THEN 'ELIGIBLE'
        ELSE 'NOT ELIGIBLE'
    END as JobBoardEligibility,
    CASE 
        WHEN j.Enable = 0 THEN 'Job Disabled'
        WHEN j.HasReject = 1 THEN 'Job Has Reject'
        WHEN j.SupplyArtworkApproval != 0 THEN 'Supply Artwork Approval Needed'
        WHEN j.ReadyArtworkApproval != 0 THEN 'Ready Artwork Approval Needed'
        WHEN j.ProofStatus != 0 THEN 'Proof Status Not None'
        WHEN j.NeedApproval = 1 THEN 'Needs Approval'
        ELSE 'Should be eligible'
    END as ReasonIfNotEligible
FROM Job j
WHERE j.RunId IN (375367, 375335)
ORDER BY j.RunId, j.Id;

-- 6. Look for similar patterns in other recent ganged digital runs
PRINT '';
PRINT '=== SIMILAR GANGED DIGITAL RUNS PATTERN ===';

SELECT TOP 20
    r.Id as RunId,
    r.Status as RunStatus,
    r.PrintType,
    COUNT(j.Id) as JobCount,
    COUNT(CASE WHEN j.Status = 23 THEN 1 END) as DPCCompleteJobs,
    COUNT(CASE WHEN j.Status = 22 THEN 1 END) as DPCPrintedJobs,
    MAX(j.ModifiedDate) as LastModified
FROM Run r
INNER JOIN Job j ON j.RunId = r.Id
WHERE r.PrintType = 'D'  -- Digital runs
  AND r.Id > 375000      -- Recent runs around the same time
  AND r.Id < 376000
GROUP BY r.Id, r.Status, r.PrintType
HAVING COUNT(j.Id) > 1   -- Multiple jobs (ganged)
ORDER BY r.Id DESC;

-- 7. Check for any audit trail or logging of job board changes
PRINT '';
PRINT '=== AUDIT TRAIL CHECK ===';

-- Check if there are any audit tables
SELECT 
    TABLE_NAME
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_NAME LIKE '%audit%' 
   OR TABLE_NAME LIKE '%log%'
   OR TABLE_NAME LIKE '%history%'
ORDER BY TABLE_NAME;

-- 8. Check current job board status for these runs
PRINT '';
PRINT '=== CURRENT JOB BOARD STATUS ===';

-- This would need to be checked against the actual job board logic
-- Based on the code, jobs are removed from job board when they reach certain completion statuses

SELECT 
    j.Id as JobId,
    j.RunId,
    j.Status,
    CASE 
        WHEN j.Status >= 28 THEN 'COMPLETED - Should be removed from job board'
        WHEN j.Status = 23 THEN 'DPC COMPLETE - May be removed from job board'
        ELSE 'Should still be on job board'
    END as ExpectedJobBoardStatus
FROM Job j
WHERE j.RunId IN (375367, 375335)
ORDER BY j.RunId, j.Id;

PRINT '';
PRINT '=== INVESTIGATION COMPLETE ===';
PRINT 'Check the results above to understand:';
PRINT '1. Current status of the runs and jobs';
PRINT '2. Whether jobs meet job board eligibility criteria';
PRINT '3. If DPCComplete status is causing removal from job boards';
PRINT '4. Similar patterns in other ganged digital runs';
