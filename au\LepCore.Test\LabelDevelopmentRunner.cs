using System;
using System.Drawing;
using System.Drawing.Printing;
using System.IO;
using System.Windows.Forms;
using lep.despatch;
using lep.despatch.impl.label;
using lep.job;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace LepCore.Test
{
    /// <summary>
    /// Hot reload development runner for label comparison
    /// Run this for quick iterative development with live preview
    /// </summary>
    [TestClass]
    public class LabelDevelopmentRunner
    {
        private static LepFixture lep;

        [ClassInitialize]
        public static void ClassInitialize(TestContext context)
        {
            lep = new LepFixture();
        }

        [TestMethod]
        public void HotReload_LabelComparison()
        {
            var job = lep.JobApp.GetJob(1972993);
            
            // Create labels with proper filenames
            var originalFilename = Path.Combine(@"c:\Lepdata\0", $"original_{DateTime.Now.Ticks}.xps");
            var unifiedFilename = Path.Combine(@"c:\Lepdata\0", $"unified_{DateTime.Now.Ticks}.xps");

            var originalLabel = CreateOriginalLabel(job, originalFilename);
            var unifiedLabel = CreateUnifiedLabel(job, unifiedFilename);

            // Show live comparison for hot reload development
            ShowLiveComparison(originalLabel, unifiedLabel);
        }

        private FurtherPocessingThumbnailLabel CreateOriginalLabel(IJob job, string filename)
        {
            var label = new FurtherPocessingThumbnailLabel();
            label.ConfigurationApplication = lep.ConfigApp;
            label.PrinterAndTray = "Microsoft XPS Document Writer";
            label.PrintFileName = filename;
            label.Job = job;
            label.SetupPrintProperties();
            return label;
        }

        private UnifiedFurtherProcessingLabel CreateUnifiedLabel(IJob job, string filename)
        {
            var label = new UnifiedFurtherProcessingLabel(
                job,
                LabelType.FurtherProcessing,
                "Microsoft XPS Document Writer",
                lep.ConfigApp,
                filename
            );
            label.SetupPrintProperties();
            return label;
        }

        private void ShowLiveComparison(PrintDocument originalLabel, PrintDocument unifiedLabel)
        {
            using (var form = new HotReloadComparisonForm(originalLabel, unifiedLabel))
            {
                form.ShowDialog();
            }
        }
    }

    /// <summary>
    /// Enhanced comparison form with hot reload capabilities
    /// </summary>
    public class HotReloadComparisonForm : Form
    {
        private PrintDocument _originalLabel;
        private PrintDocument _unifiedLabel;
        private Panel _leftPanel;
        private Panel _rightPanel;
        private Button _refreshButton;
        private CheckBox _actualSizeCheckBox;
        private Label _infoLabel;
        private bool _showActualSize = true;

        public HotReloadComparisonForm(PrintDocument originalLabel, PrintDocument unifiedLabel)
        {
            _originalLabel = originalLabel;
            _unifiedLabel = unifiedLabel;
            InitializeHotReloadForm();
        }

        private void InitializeHotReloadForm()
        {
            this.Text = "Hot Reload Label Comparison - Press F5 to Refresh";
            this.Size = new Size(1600, 1000);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.KeyPreview = true;
            this.KeyDown += Form_KeyDown;

            // Create main layout
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                Padding = new Padding(10)
            };

            // Set styles
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40F)); // Controls
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F)); // Labels
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F)); // Content

            // Create controls panel
            var controlsPanel = new Panel { Dock = DockStyle.Fill };
            
            _refreshButton = new Button
            {
                Text = "Refresh (F5)",
                Size = new Size(100, 30),
                Location = new Point(10, 5)
            };
            _refreshButton.Click += RefreshButton_Click;

            _actualSizeCheckBox = new CheckBox
            {
                Text = "Actual Size (for overlay)",
                Checked = _showActualSize,
                Location = new Point(120, 8),
                Size = new Size(150, 25)
            };
            _actualSizeCheckBox.CheckedChanged += ActualSizeCheckBox_CheckedChanged;

            _infoLabel = new Label
            {
                Text = "Press F5 to refresh after code changes",
                Location = new Point(280, 10),
                Size = new Size(300, 25),
                ForeColor = Color.Blue
            };

            controlsPanel.Controls.AddRange(new Control[] { _refreshButton, _actualSizeCheckBox, _infoLabel });

            // Create title labels
            var leftTitle = new Label
            {
                Text = "Original FurtherProcessingThumbnailLabel",
                Font = new Font("Arial", 12, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill,
                BackColor = Color.LightCoral
            };

            var rightTitle = new Label
            {
                Text = "New UnifiedFurtherProcessingLabel",
                Font = new Font("Arial", 12, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill,
                BackColor = Color.LightBlue
            };

            // Create comparison panels
            _leftPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White,
                AutoScroll = true
            };

            _rightPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White,
                AutoScroll = true
            };

            _leftPanel.Paint += LeftPanel_Paint;
            _rightPanel.Paint += RightPanel_Paint;

            // Add to layout
            mainPanel.Controls.Add(controlsPanel, 0, 0);
            mainPanel.SetColumnSpan(controlsPanel, 2);
            mainPanel.Controls.Add(leftTitle, 0, 1);
            mainPanel.Controls.Add(rightTitle, 1, 1);
            mainPanel.Controls.Add(_leftPanel, 0, 2);
            mainPanel.Controls.Add(_rightPanel, 1, 2);

            this.Controls.Add(mainPanel);
        }

        private void Form_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.F5)
            {
                RefreshLabels();
            }
        }

        private void RefreshButton_Click(object sender, EventArgs e)
        {
            RefreshLabels();
        }

        private void ActualSizeCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            _showActualSize = _actualSizeCheckBox.Checked;
            _leftPanel.Invalidate();
            _rightPanel.Invalidate();
        }

        private void RefreshLabels()
        {
            _infoLabel.Text = "Refreshing...";
            _infoLabel.ForeColor = Color.Orange;
            
            try
            {
                // Force refresh of the labels
                _leftPanel.Invalidate();
                _rightPanel.Invalidate();
                
                _infoLabel.Text = $"Refreshed at {DateTime.Now:HH:mm:ss}";
                _infoLabel.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                _infoLabel.Text = $"Error: {ex.Message}";
                _infoLabel.ForeColor = Color.Red;
            }
        }

        private void LeftPanel_Paint(object sender, PaintEventArgs e)
        {
            DrawLabel(e, _originalLabel, Color.Red, "Original");
        }

        private void RightPanel_Paint(object sender, PaintEventArgs e)
        {
            DrawLabel(e, _unifiedLabel, Color.Blue, "Unified");
        }

        private void DrawLabel(PaintEventArgs e, PrintDocument document, Color borderColor, string labelType)
        {
            if (document == null) return;

            try
            {
                var bitmap = RenderDocumentToBitmap(document);
                if (bitmap == null) return;

                var panel = e.ClipRectangle;
                
                if (_showActualSize)
                {
                    // Draw at actual size for overlay comparison
                    var x = (panel.Width - bitmap.Width) / 2;
                    var y = (panel.Height - bitmap.Height) / 2;
                    
                    e.Graphics.DrawImage(bitmap, x, y, bitmap.Width, bitmap.Height);
                    
                    // Draw border
                    using (var pen = new Pen(borderColor, 2))
                    {
                        e.Graphics.DrawRectangle(pen, x - 1, y - 1, bitmap.Width + 2, bitmap.Height + 2);
                    }
                    
                    // Size info
                    using (var font = new Font("Arial", 10))
                    using (var brush = new SolidBrush(borderColor))
                    {
                        var sizeText = $"{labelType}: {bitmap.Width}x{bitmap.Height}px (Actual Size)";
                        e.Graphics.DrawString(sizeText, font, brush, new PointF(10, 10));
                    }
                }
                else
                {
                    // Scale to fit
                    var scaleX = (float)panel.Width / bitmap.Width;
                    var scaleY = (float)panel.Height / bitmap.Height;
                    var scale = Math.Min(scaleX, scaleY) * 0.9f; // 90% to leave margin

                    var scaledWidth = (int)(bitmap.Width * scale);
                    var scaledHeight = (int)(bitmap.Height * scale);
                    var x = (panel.Width - scaledWidth) / 2;
                    var y = (panel.Height - scaledHeight) / 2;

                    e.Graphics.DrawImage(bitmap, x, y, scaledWidth, scaledHeight);
                }
                
                bitmap.Dispose();
            }
            catch (Exception ex)
            {
                using (var font = new Font("Arial", 12))
                using (var brush = new SolidBrush(Color.Red))
                {
                    e.Graphics.DrawString($"Error rendering {labelType}: {ex.Message}", 
                        font, brush, new PointF(10, 50));
                }
            }
        }

        private Bitmap RenderDocumentToBitmap(PrintDocument document)
        {
            var bitmap = new Bitmap(800, 1200);
            
            using (var graphics = Graphics.FromImage(bitmap))
            {
                graphics.Clear(Color.White);
                
                var pageSettings = document.DefaultPageSettings;
                var printableArea = new Rectangle(
                    pageSettings.Margins.Left,
                    pageSettings.Margins.Top,
                    pageSettings.PaperSize.Width - pageSettings.Margins.Left - pageSettings.Margins.Right,
                    pageSettings.PaperSize.Height - pageSettings.Margins.Top - pageSettings.Margins.Bottom
                );

                var args = new PrintPageEventArgs(
                    graphics,
                    printableArea,
                    new Rectangle(0, 0, pageSettings.PaperSize.Width, pageSettings.PaperSize.Height),
                    pageSettings
                );

                var printPageMethod = document.GetType().GetMethod("OnPrintPage", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                printPageMethod?.Invoke(document, new object[] { args });
            }

            return bitmap;
        }
    }
}
