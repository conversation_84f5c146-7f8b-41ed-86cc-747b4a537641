# PowerShell script to run the ganged runs investigation
# This script will execute the SQL investigation and the C# test

param(
    [string]$Environment = "local"  # local, icemedia, app07, etc.
)

Write-Host "Ganged Digital Runs Investigation" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green
Write-Host "Investigating Run Numbers: 375367, 375335" -ForegroundColor Yellow
Write-Host ""

# Determine connection string based on environment
$connectionString = ""
switch ($Environment.ToLower()) {
    "local" { 
        $connectionString = "Data Source=.;user id=**;password=**;Initial Catalog=PRD_AU;MultipleActiveResultSets=true"
    }
    "icemedia" { 
        $connectionString = "Data Source=newman; user id=**; password=*************; Initial Catalog=PRD_AU_2020_03_30;MultipleActiveResultSets=true"
    }
    "app07" { 
        $connectionString = "Data Source=srv03; user id=**; password=*************; Initial Catalog=PRD_AU;MultipleActiveResultSets=true"
    }
    "henry" { 
        $connectionString = "Data Source=*************; user id=**; password=*************; Initial Catalog=PRD_AU;MultipleActiveResultSets=true"
    }
    default { 
        Write-Host "Unknown environment: $Environment" -ForegroundColor Red
        Write-Host "Available environments: local, icemedia, app07, henry" -ForegroundColor Yellow
        exit 1
    }
}

Write-Host "Using environment: $Environment" -ForegroundColor Cyan
Write-Host "Connection: $($connectionString.Split(';')[0])" -ForegroundColor Cyan
Write-Host ""

# 1. Run SQL Investigation
Write-Host "1. Running SQL Investigation..." -ForegroundColor Yellow
Write-Host "================================" -ForegroundColor Yellow

try {
    # Check if sqlcmd is available
    $sqlcmdPath = Get-Command sqlcmd -ErrorAction SilentlyContinue
    if ($sqlcmdPath) {
        Write-Host "Executing SQL investigation script..." -ForegroundColor Green
        
        # Parse connection string components
        $parts = $connectionString.Split(';')
        $server = ($parts | Where-Object { $_ -like "Data Source=*" }).Split('=')[1]
        $database = ($parts | Where-Object { $_ -like "Initial Catalog=*" }).Split('=')[1]
        $userId = ($parts | Where-Object { $_ -like "user id=*" }).Split('=')[1]
        $password = ($parts | Where-Object { $_ -like "password=*" }).Split('=')[1]
        
        # Run the SQL script
        $sqlOutput = sqlcmd -S $server -d $database -U $userId -P $password -i "investigate_ganged_runs_di**ppearing.sql" -W
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "SQL Investigation Results:" -ForegroundColor Green
            Write-Host "=========================" -ForegroundColor Green
            $sqlOutput | ForEach-Object { Write-Host $_ }
        } else {
            Write-Host "SQL execution failed with exit code: $LASTEXITCODE" -ForegroundColor Red
        }
    } else {
        Write-Host "sqlcmd not found. Please install SQL Server Command Line Utilities." -ForegroundColor Red
        Write-Host "Alternative: Run the SQL script manually in SQL Server Management Studio" -ForegroundColor Yellow
        Write-Host "SQL Script location: investigate_ganged_runs_di**ppearing.sql" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Error running SQL investigation: $($_.Exception.Mes**ge)" -ForegroundColor Red
}

Write-Host ""

# 2. Run C# Investigation
Write-Host "2. Running C# Investigation..." -ForegroundColor Yellow
Write-Host "==============================" -ForegroundColor Yellow

try {
    Write-Host "Building and running C# investigation test..." -ForegroundColor Green
    
    # Build the test project
    $buildResult = dotnet build LepCore.Test\LepCore.Test.csproj --verbosity minimal
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build successful. Running investigation test..." -ForegroundColor Green
        
        # Run the specific investigation test
        dotnet test LepCore.Test\LepCore.Test.csproj --filter "Investigate_GangedRuns_375367_375335" --verbosity normal --logger "console;verbosity=detailed"
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "C# Investigation completed successfully." -ForegroundColor Green
        } else {
            Write-Host "C# Investigation test failed." -ForegroundColor Red
        }
    } else {
        Write-Host "Build failed. Cannot run C# investigation." -ForegroundColor Red
    }
} catch {
    Write-Host "Error running C# investigation: $($_.Exception.Mes**ge)" -ForegroundColor Red
}

Write-Host ""

# 3. Summary and Next Steps
Write-Host "3. Investigation Summary" -ForegroundColor Yellow
Write-Host "========================" -ForegroundColor Yellow

Write-Host "Investigation completed for ganged digital runs di**ppearing from job boards." -ForegroundColor Green
Write-Host ""
Write-Host "Key areas investigated:" -ForegroundColor Cyan
Write-Host "- Run status and job details for runs 375367 and 375335" -ForegroundColor White
Write-Host "- Job board eligibility criteria" -ForegroundColor White
Write-Host "- DPC Complete scanning logic" -ForegroundColor White
Write-Host "- Impact on ganged digital runs" -ForegroundColor White
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Review the SQL results to understand current run/job status" -ForegroundColor White
Write-Host "2. Check the C# investigation output for job board eligibility logic" -ForegroundColor White
Write-Host "3. Look for patterns in the barcode scanning logic that might cause removal" -ForegroundColor White
Write-Host "4. Consider if DPCComplete status should keep ganged runs visible on job boards" -ForegroundColor White
Write-Host ""
Write-Host "Files created for investigation:" -ForegroundColor Cyan
Write-Host "- investigate_ganged_runs_di**ppearing.sql (SQL investigation)" -ForegroundColor White
Write-Host "- GangedRunInvestigation.cs (C# investigation test)" -ForegroundColor White
Write-Host "- run-ganged-investigation.ps1 (this script)" -ForegroundColor White

Write-Host ""
Write-Host "Investigation complete!" -ForegroundColor Green
